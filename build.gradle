plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.8'
    id 'io.spring.dependency-management' version '1.1.6'
    id "org.sonarqube" version "4.4.1.3373"
    id "jacoco"
}

jacoco {
    toolVersion = "0.8.10"
}

group = 'com.concirrus'
version = '0.0.1-SNAPSHOT'

jar {
    archivesBaseName = "certificate-issuance-platform-service"
    project.version = ""
}

java {
    sourceCompatibility = '17'
}

ext {
    set('springCloudGcpVersion', "5.4.3")
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'javax.persistence:javax.persistence-api:2.2'
    compileOnly 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'
    implementation 'org.springframework.boot:spring-boot-starter-web'

    // https://mvnrepository.com/artifact/org.owasp.encoder/encoder
    implementation 'org.owasp.encoder:encoder:1.2.3'
    // mongo
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    // kubernetes
    implementation 'org.springframework.cloud:spring-cloud-starter-kubernetes-client-config:2.1.3'

    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.14.0'

    implementation 'jakarta.validation:jakarta.validation-api:3.0.0'

    // https://mvnrepository.com/artifact/org.apache.commons/commons-text
    implementation 'org.apache.commons:commons-text:1.11.0'

    implementation 'org.springframework.cloud:spring-cloud-starter-aws-messaging:2.2.6.RELEASE'
    implementation "org.springframework.integration:spring-integration-core"
    implementation 'com.google.cloud:spring-cloud-gcp-starter-pubsub'

    implementation 'org.springframework.boot:spring-boot-starter-validation:3.2.2'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.17.1'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.17.1'

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-aop
    implementation 'org.springframework.boot:spring-boot-starter-aop:3.3.0'

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-cache
    implementation 'org.springframework.boot:spring-boot-starter-cache:3.2.4'

    // https://mvnrepository.com/artifact/com.github.ben-manes.caffeine/caffeine
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

}

dependencyManagement {
    imports {
        mavenBom "com.google.cloud:spring-cloud-gcp-dependencies:${springCloudGcpVersion}"
    }
}

sonarqube {
    properties {
        property 'sonar.coverage.exclusions', "**/com/concirrus/certificateservice/annotation/**," +
                "**/com/concirrus/certificateservice/config/**," +
                "**/com/concirrus/certificateservice/constant/**," +
                "**/com/concirrus/certificateservice/dto/**," +
                "**/com/concirrus/certificateservice/entity/**," +
                "**/com/concirrus/certificateservice/enums/**," +
                "**/com/concirrus/certificateservice/handler/**," +
                "**/com/concirrus/certificateservice/listener/**," +
                "**/com/concirrus/certificateservice/model/**," +
                "**/com/concirrus/certificateservice/sal/**," +
                "**/com/concirrus/certificateservice/dal/**," +
                "**/com/concirrus/certificateservice/CertificateServiceApplication.java"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        xml.destination file("${buildDir}/reports/jacoco.xml")
    }
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
}
