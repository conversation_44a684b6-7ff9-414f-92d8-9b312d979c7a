package com.concirrus.certificateservice.service.impl;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import com.concirrus.certificateservice.dal.CertificateDAL;
import com.concirrus.certificateservice.dto.CertificateCountResponse;
import com.concirrus.certificateservice.dto.CertificateFilterRequest;
import com.concirrus.certificateservice.dto.UserEntity;
import com.concirrus.certificateservice.model.exception.BadRequestException;
import com.concirrus.certificateservice.model.exception.NoDataFoundException;
import com.concirrus.certificateservice.redis.pubsub.CertificateUpdateRedisPublisher;
import com.concirrus.certificateservice.sal.HullUserSAL;
import com.concirrus.certificateservice.service.CertificateService;
import com.concirrus.certificateservice.service.IEventMessageService;
import com.concirrus.certificateservice.utils.LoggingUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.concirrus.certificateservice.constant.CommonConstants.*;
import static com.concirrus.certificateservice.constant.DbConstants.*;
import static com.concirrus.certificateservice.constant.enums.CertificateStatus.*;
import static com.concirrus.certificateservice.utils.PayloadGenerator.generateCertificatePayload;
import static com.concirrus.certificateservice.utils.PayloadGenerator.generateCertificateStatusEmailPayload;

@Slf4j
@Service
public class CertificateServiceImpl implements CertificateService {

    private final CertificateDAL certificateDAL;
    private final String documentQueueUrl;
    private final IEventMessageService eventMessageService;
    private final String submissionQueueUrl;
    private final HullUserSAL hullUserSAL;
    private final CertificateUpdateRedisPublisher redisPublisher;

    public CertificateServiceImpl(CertificateDAL certificateDAL,
                                 @Value("${cloud.queue.out.document}") String documentQueueUrl,
                                 IEventMessageService eventMessageService,
                                 @Value("${cloud.queue.out.submission}") String submissionQueueUrl,
                                 HullUserSAL hullUserSAL,
                                 CertificateUpdateRedisPublisher redisPublisher) {
        this.certificateDAL = certificateDAL;
        this.documentQueueUrl = documentQueueUrl;
        this.eventMessageService = eventMessageService;
        this.submissionQueueUrl = submissionQueueUrl;
        this.hullUserSAL = hullUserSAL;
        this.redisPublisher = redisPublisher;
    }

    /**
     * Creates a new certificate or updates an existing one based on the presence of a certificate ID.
     *
     * @param certificateRequest The certificate document containing all certificate data
     * @param clientId The client identifier
     * @param userId The user identifier
     * @return The created or updated certificate document
     */
    @Override
    public Document createOrUpdateCertificate(Document certificateRequest, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(clientId, userId));
        String certificateId = certificateRequest.getString(CERTIFICATE_ID);
        if (StringUtils.isNotEmpty(certificateId)) {
            log.info("Updating existing certificate with ID: {}", certificateId);
            return updateCertificate(certificateId, certificateRequest, clientId, userId);
        } else {
            log.info("Creating new certificate as no certificateId was provided");
            return createCertificate(certificateRequest, clientId, userId);
        }
    }

    /**
     * Creates a new certificate with the provided information.
     * Validates the certificate, generates new IDs, and prepares it for saving.
     *
     * @param certificateRequest The certificate document containing all certificate data
     * @param clientId The client identifier
     * @param userId The user identifier
     * @return The newly created certificate document
     */
    @Override
    public Document createCertificate(Document certificateRequest, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(clientId, userId));

        // if status of certificate is not DRAFT, or is empty/missing, then check validation
        String status = certificateRequest.getString(STATUS);
        if (StringUtils.isEmpty(status) || !status.equals(REVIEW.getValue())) {
            validateCertificateForIssuance(certificateRequest);
        }

        UUID documentId = UUID.randomUUID();
        UUID certificateId = UUID.randomUUID();

        prepareDocumentForSave(certificateRequest, certificateId, clientId, userId, documentId);
        setUserInfo(certificateRequest, userId, clientId);

        log.info(LoggingUtils.logMethodExit());
        return certificateDAL.save(certificateRequest);
    }

    /**
     * Updates an existing certificate with the provided information.
     * Validates the certificate and updates its status based on approval requirements.
     *
     * @param certificateId The unique identifier of the certificate to update
     * @param certificateRequest The certificate document containing updated data
     * @param clientId The client identifier
     * @param userId The user identifier
     * @return The updated certificate document
     * @throws BadRequestException If there's an error during the update process
     */
    @Override
    public Document updateCertificate(String certificateId, Document certificateRequest, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(certificateId, certificateRequest, clientId, userId));
        try {
            // if status of certificate is not DRAFT then check validation
            String status = certificateRequest.getString(STATUS);
            if (status != null && !status.equals(REVIEW.getValue())) {
                validateCertificateForIssuance(certificateRequest);
            }

            Boolean approvalRequired = (Boolean) certificateRequest.get(APPROVAL_REQUIRED);
            Boolean changesRequired = (Boolean) certificateRequest.get(CHANGES_REQUIRED);

            // Generate certificate number and name if approval is not required or explicitly required
            boolean shouldGenerateCertificateInfo = (approvalRequired != null && !approvalRequired) ||
                                                   (approvalRequired != null && approvalRequired);

            // Generate certificate number and name only if approval is explicitly set (true or false)
            if (shouldGenerateCertificateInfo) {
                if (StringUtils.isEmpty(certificateRequest.getString(CERTIFICATE_NUMBER))) {
                    setCertificateInfo(certificateRequest, clientId);
                }
                if (StringUtils.isEmpty(certificateRequest.getString(CERTIFICATE_NAME))) {
                    setCertificateName(certificateRequest);
                }
            }

            if (approvalRequired != null && !approvalRequired) {
                certificateRequest.put(STATUS, ISSUED);
                certificateRequest.put(GENERATED_AT, formatGeneratedAtDate(LocalDateTime.now()));
                log.info("Certificate with ID {} is approved and will be issued", certificateId);
                eventMessageService.publishMessage(documentQueueUrl, generateCertificatePayload(clientId, certificateRequest));
            }

            if ((approvalRequired != null && approvalRequired) || (changesRequired != null && changesRequired)) {
                certificateRequest.put(STATUS, PENDING);
                // Publish status update to Redis for SSE
                redisPublisher.publishUpdate(clientId, certificateId);
            }

            certificateRequest.put(UPDATED_AT, LocalDateTime.now());
            certificateRequest.put(UPDATED_BY, userId);
            // remove immutable ID from the update object
            certificateRequest.remove(MONGO_ID);
            log.info(LoggingUtils.logMethodExit());
            return certificateDAL.updateCertificateById(certificateId, certificateRequest, clientId, userId);
        } catch (Exception e) {
            log.error("Error while updating certificate with ID {}: {}", certificateId, e.getMessage());
            throw new BadRequestException("Error while updating certificate: " + e.getMessage());
        }
    }

    /**
     * Retrieves a certificate by its unique identifier.
     *
     * @param certificateId The unique identifier of the certificate
     * @param clientId The client identifier
     * @param userId The user identifier
     * @return The certificate document
     * @throws NoDataFoundException If no certificate is found with the given ID
     */
    @Override
    public Document getCertificateById(String certificateId, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(certificateId, clientId, userId));
        Document certificate = certificateDAL.getCertificateById(certificateId, clientId, userId);
        if (certificate == null) {
            log.warn("Certificate with ID {} not found for client {}", certificateId, clientId);
            throw new NoDataFoundException("Certificate not found");
        }
        log.info(LoggingUtils.logMethodExit());
        return certificate;
    }

    /**
     * Retrieves all certificates matching the provided filter criteria.
     *
     * @param certificateFilterRequest The filter criteria for certificates
     * @param pageRequest Pagination information
     * @param clientId The client identifier
     * @param userId The user identifier
     * @return A pair containing the total count and list of matching certificates
     */
    @Override
    public Pair<Long, List<Document>> getAllCertificates(CertificateFilterRequest certificateFilterRequest, PageRequest pageRequest, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(certificateFilterRequest, pageRequest, clientId, userId));
        Pair<Long, List<Document>> certificatesData = certificateDAL.getAllCertificatesBy(certificateFilterRequest, pageRequest, clientId, userId);
        log.info(LoggingUtils.logMethodExit());
        return certificatesData;
    }

    /**
     * Sends a certificate-related email notification.
     *
     * @param email The recipient email address
     * @param template The email template to use
     * @param clientId The client identifier
     */
    @Override
    public void sendCertificateEmail(String name, String email, String template, String clientId, String lob) {
        log.info(LoggingUtils.logMethodEntry(name, email, template, clientId, lob));
        try {
            eventMessageService.publishMessage(submissionQueueUrl, generateCertificateStatusEmailPayload(name, email, template, clientId, lob));
        } catch (JsonProcessingException e) {
            log.error("Error while generating certificate status email payload", e);
        }
        log.info(LoggingUtils.logMethodExit());
    }

    /**
     * Gets the count of certificate requests matching the provided criteria.
     *
     * @param clientId The client identifier
     * @param approvalRequired Flag indicating if approval is required
     * @param approvalStatus List of approval statuses to filter by
     * @param changesRequired Flag indicating if changes are required
     * @param changeRequestStatus List of change request statuses to filter by
     * @param userId The user identifier
     * @param status List of certificate statuses to filter by
     * @return The count of matching certificate requests
     */
    @Override
    public long getCertificatesRequestsCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status) {
        log.info(LoggingUtils.logMethodEntry(clientId));
        long count = certificateDAL.getCertificatesRequestsCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, userId, status);
        log.info(LoggingUtils.logMethodExit());
        return count;
    }

    /**
     * Gets detailed counts of certificate requests including total, approval requests, and change requests.
     *
     * @param clientId The client identifier
     * @param approvalRequired Flag indicating if approval is required
     * @param approvalStatus List of approval statuses to filter by
     * @param changesRequired Flag indicating if changes are required
     * @param changeRequestStatus List of change request statuses to filter by
     * @param userId The user identifier
     * @param status List of certificate statuses to filter by
     * @return CertificateCountResponse containing detailed count information
     */
    @Override
    public CertificateCountResponse getCertificatesRequestsDetailedCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status) {
        log.info(LoggingUtils.logMethodEntry(clientId));
        CertificateCountResponse response = certificateDAL.getCertificatesRequestsDetailedCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, userId, status);
        log.info(LoggingUtils.logMethodExit());
        return response;
    }

    /**
     * Updates the status of a certificate.
     * If the certificate is approved, it will be issued and a document will be generated.
     *
     * @param certificateId The unique identifier of the certificate
     * @param certificateRequest The certificate document (optional, will be fetched if null)
     * @param approvalStatus The new approval status
     * @param changeRequestStatus The new change request status
     * @param clientId The client identifier
     * @param userId The user identifier
     * @return The updated certificate document
     * @throws NoDataFoundException If the certificate is not found
     * @throws BadRequestException If there's an error during the update process
     */
    @Override
    public Document updateCertificateStatus(String certificateId, Document certificateRequest, CertificateStatus approvalStatus, CertificateStatus changeRequestStatus, String clientId, String userId) {
        try {
            log.info(LoggingUtils.logMethodEntry(certificateId, clientId, userId));
            // If certificateRequest is null, get the document by ID
            if (ObjectUtils.isEmpty(certificateRequest)) {
                // sending null userId because the certificate can be made by broker/insured also, so we are not filtering by userId
                certificateRequest = certificateDAL.getCertificateById(certificateId, clientId, null);
                if (certificateRequest == null) {
                    log.warn("Certificate with ID {} not found for client {}", certificateId, clientId);
                    throw new NoDataFoundException("Certificate not found");
                }
            }
            certificateRequest.put(UPDATED_AT, LocalDateTime.now());
            certificateRequest.put(UPDATED_BY, userId);

            String statusMessage = null;

            if (approvalStatus != null) {
                certificateRequest.put(APPROVAL_STATUS, approvalStatus.getValue());
                if (approvalStatus == CertificateStatus.APPROVED) {
                    certificateRequest.put(STATUS, ISSUED.getValue());
                    certificateRequest.put(GENERATED_AT, formatGeneratedAtDate(LocalDateTime.now()));
                    statusMessage = "Certificate approved and issued";
                    sendCertificateEmail(certificateRequest.getString(NAME), certificateRequest.getString(EMAIL), CERTIFICATE_APPROVED, clientId, LOGISTICS);
                    eventMessageService.publishMessage(documentQueueUrl, generateCertificatePayload(clientId, certificateRequest));
                }
                else if (approvalStatus == DECLINED) {
                    certificateRequest.put(STATUS, DECLINED.getValue());
                    statusMessage = "Certificate declined";
                    sendCertificateEmail(certificateRequest.getString(NAME), certificateRequest.getString(EMAIL), CERTIFICATE_DECLINED, clientId, LOGISTICS);
                }
                //set to false after approval or decline
                certificateRequest.put(APPROVAL_REQUIRED, false);
            }
            if (changeRequestStatus != null) {
                certificateRequest.put(CHANGE_REQUEST_STATUS, changeRequestStatus.getValue());
                if (changeRequestStatus == CertificateStatus.APPROVED) {
                    certificateRequest.put(STATUS, ISSUED.getValue());
                    certificateRequest.put(DOCUMENT_ID, UUID.randomUUID().toString());
                    certificateRequest.put(GENERATED_AT, formatGeneratedAtDate(LocalDateTime.now()));
                    statusMessage = "Certificate request changes approved and issued";
                    eventMessageService.publishMessage(documentQueueUrl, generateCertificatePayload(clientId, certificateRequest));
                } else if (changeRequestStatus == DECLINED) {
                    statusMessage = "Change request updated";
                }
                //set to false after approval or decline
                certificateRequest.put(CHANGES_REQUIRED, false);
            }

            // Remove immutable ID from the update object
            certificateRequest.remove(MONGO_ID);
            // sending null userId because the certificate can be made by broker/insured also, so we are not filtering by userId
            Document result = certificateDAL.updateCertificateById(certificateId, certificateRequest, clientId, null);

            // Publish status update to Redis for SSE
            if (statusMessage != null) {
                String status = certificateRequest.getString(STATUS);
                redisPublisher.publishCertificateStatusUpdate(clientId, certificateId, status, statusMessage);
            }

            log.info(LoggingUtils.logMethodExit());
            return result;
        } catch (Exception e) {
            log.error("Error while updating certificate status with ID {}: {}", certificateId, e.getMessage());
            throw new BadRequestException("Error while updating certificate status: " + e.getMessage());
        }
    }

    /**
     * Helper method to prepare document with common fields before saving
     *
     * @param document The document to prepare
     * @param certificateId Certificate ID
     * @param clientId Client ID
     * @param userId User ID
     * @param documentId Document ID
     */
    private void prepareDocumentForSave(Document document, UUID certificateId, String clientId,
                                        String userId, UUID documentId) {
        LocalDateTime now = LocalDateTime.now();
        document.put(CERTIFICATE_ID, certificateId.toString());
        document.put(MONGO_CLIENT_ID, clientId);
        document.put(CREATED_BY, userId);
        document.put(DOCUMENT_ID, documentId.toString());
        document.put(CREATED_AT, now.toString());
        document.put(UPDATED_BY, userId);
        document.put(STATUS, DRAFT);
        document.put(UPDATED_AT, now.toString());
        document.put(IS_DELETED, false);
    }

    /**
     * Sets user information in the certificate document by fetching user details
     * from the user service.
     *
     * @param document The certificate document
     * @param userId The user identifier
     * @param clientId The client identifier
     */
    private void setUserInfo(Document document, String userId, String clientId) {
        UserEntity userInfo = hullUserSAL.getUserByKeycloakId(userId, true, clientId);
        if (ObjectUtils.isNotEmpty(userInfo)) {
            document.put(NAME, userInfo.getFullName());
            document.put(EMAIL, userInfo.getEmail());
        }
    }

    /**
     * Sets certificate-specific information such as certificate number
     * based on policy number, insured client ID and previous certificates.
     *
     * @param document The certificate document
     * @param clientId The client identifier
     */
    private void setCertificateInfo(Document document, String clientId) {
        String policyNumber = document.getString(POLICY_NUMBER);
        String insuredClientId = document.getString(INSURED_CLIENT_ID);
        long previousCertificatesCount = certificateDAL.getPreviousCertificatesCountByPolicyNumber(policyNumber, clientId);
        // here not using AL as L is already present in the userClientId
        String certificateNumber = "A" + insuredClientId + String.format("%07d", previousCertificatesCount + 1);
        log.info("Setting certificate number: {} for policy number: {} and insured client ID: {}", certificateNumber, policyNumber, insuredClientId);
        document.put(CERTIFICATE_NUMBER, certificateNumber);
    }

    /**
     * Validates a certificate before it can be issued.
     * Checks minimum certificate value, override limits, and conveyance limits.
     *
     * @param certificate The certificate document to validate
     * @throws BadRequestException if validation fails
     */
    private void validateCertificateForIssuance(Document certificate) {
        log.info("Validating certificate for issuance: {}", certificate.getString(CERTIFICATE_ID));

        // Get all values needed for validation
        Double invoiceValue = getDoubleValue(certificate, INVOICE_VALUE);
        Double minimumPerCertificate = getDoubleValue(certificate, MINIMUM_PER_CERTIFICATE);
        Double overrideLimit = getDoubleValue(certificate, COMMODITY_LIMIT);
        Double conveyanceLimit = getDoubleValue(certificate, CONVEYANCE_LIMIT);
        String commodityType = certificate.getString(COMMODITY_TYPE);

        // 1. Check if Quote Structure is Deposit Premium Adjusted Annually and validate minimum certificate value
        String quoteStructure = certificate.getString(QUOTE_STRUCTURE);
        if (DEPOSIT_PREMIUM_ADJUSTED_ANNUALLY.equals(quoteStructure)) {
            if (minimumPerCertificate != null && invoiceValue != null && invoiceValue < minimumPerCertificate) {
                String errorMessage = "The Value for the Certificate you are trying to generate is lower than " +
                        formatNumber(minimumPerCertificate) + " (Minimum per Certificate). Please select a larger value or communicate with your broker regarding this Certificate.";
                log.warn("Certificate validation failed: {}", errorMessage);
                throw new BadRequestException(errorMessage);
            }
        }

        // 2. Check if certificate is within Override Limit and Conveyance Limit
        if (invoiceValue != null) {
            // Check if invoice value exceeds override limit
            if (overrideLimit != null && overrideLimit > 0 && invoiceValue > overrideLimit) {
                String errorMessage = "This certificate invoice value (" + formatNumber(invoiceValue) +
                        ") exceeds the Override Limit of " + formatNumber(overrideLimit) +
                        " for commodity type '" + (commodityType != null ? commodityType : "unknown") + "'.";
                log.warn("Certificate validation failed: {}", errorMessage);
                throw new BadRequestException(errorMessage);
            }

            // Check if invoice value exceeds conveyance limit
            if (conveyanceLimit != null && conveyanceLimit > 0 && invoiceValue > conveyanceLimit) {
                String errorMessage = "This certificate value (" + formatNumber(invoiceValue) +
                        ") exceeds the Conveyance Limit of " + formatNumber(conveyanceLimit) +
                        " for this conveyance type.";
                log.warn("Certificate validation failed: {}", errorMessage);
                throw new BadRequestException(errorMessage);
            }
        }
        log.info("Certificate validation successful for certificateId: {}", certificate.getString(CERTIFICATE_ID));
    }

    /**
     * Helper method to safely get a Double value from a Document
     *
     * @param doc The document containing the value
     * @param key The key for the value
     * @return The Double value, or null if not present or not parseable
     */
    private Double getDoubleValue(Document doc, String key) {
        Object value = doc.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("Failed to parse {} as Double: {}", key, value);
            return null;
        }
    }

    /**
     * Format a number to remove unnecessary decimal places.
     * If the number is a whole number, display it without decimal places.
     * Otherwise, display it with decimal places.
     *
     * @param value The number to format
     * @return Formatted number string
     */
    private String formatNumber(Double value) {
        if (value == null) return "0";

        // Check if the value is a whole number
        if (value == Math.floor(value)) {
            return String.format("%.0f", value); // Format as integer
        } else {
            return String.valueOf(value); // Keep decimal places
        }
    }

    /**
     * Generates a certificate name based on policy number and certificate number.
     * Format: policyNumber-formattedCertNumber (e.g., POL123-0001)
     *
     * @param certificate The certificate document
     * @return The formatted certificate name
     */
    private void setCertificateName(Document certificate) {
        String policyNumber = certificate.getString(POLICY_NUMBER);
        long previousCertificatesCount = certificateDAL.getPreviousCertificatesCountByPolicyNumber(policyNumber, certificate.getString(MONGO_CLIENT_ID));

        String certificateName = policyNumber + "-" + String.valueOf(previousCertificatesCount + 1);
        log.info("Generated certificate name: {}", certificateName);
        certificate.put(CERTIFICATE_NAME, certificateName);
    }

    /**
     * Formats a date for the generatedAt field in the format mm-dd-yyyy, hh:mm
     *
     * @param dateTime The date to format
     * @return Formatted date string
     */
    private String formatGeneratedAtDate(LocalDateTime dateTime) {
        return String.format("%02d-%02d-%04d, %02d:%02d",
                dateTime.getMonthValue(), dateTime.getDayOfMonth(), dateTime.getYear(),
                dateTime.getHour(), dateTime.getMinute());
    }
}
