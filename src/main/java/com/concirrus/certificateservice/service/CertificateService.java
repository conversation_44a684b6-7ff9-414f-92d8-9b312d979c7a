package com.concirrus.certificateservice.service;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import com.concirrus.certificateservice.dto.CertificateCountResponse;
import com.concirrus.certificateservice.dto.CertificateFilterRequest;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface CertificateService {
    Document createCertificate(Document certificateRequest, String clientId, String userId);
    Document updateCertificate(String certificateId, Document certificateRequest, String clientId, String userId);
    Document getCertificateById(String certificateId, String clientId, String userId);
    Pair<Long, List<Document>> getAllCertificates(CertificateFilterRequest certificateFilterRequest, PageRequest pageRequest, String clientId, String userId);
    Document createOrUpdateCertificate(Document certificateRequest, String clientId, String userId);
    void sendCertificateEmail(String name, String email, String template, String clientId, String lob);
    long getCertificatesRequestsCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status);
    CertificateCountResponse getCertificatesRequestsDetailedCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status);
    Document updateCertificateStatus(String certificateId, Document certificateRequest, CertificateStatus approvalStatus, CertificateStatus changeRequestStatus, String clientId, String userId);
}
