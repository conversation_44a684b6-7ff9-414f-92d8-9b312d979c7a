package com.concirrus.certificateservice.redis.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a message that needs to be retried
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RetryMessage {

    /**
     * The data to be sent
     */
    private Object data;

    /**
     * Number of retry attempts made
     */
    private int attempts;

    /**
     * Timestamp for the next retry attempt
     */
    private long nextRetryTime;
}
