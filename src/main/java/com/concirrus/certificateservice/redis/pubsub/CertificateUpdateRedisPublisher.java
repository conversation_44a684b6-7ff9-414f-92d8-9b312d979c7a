package com.concirrus.certificateservice.redis.pubsub;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.concirrus.certificateservice.config.RedisConfig.SSE_REDIS_TOPIC;

@Slf4j
@Service
public class CertificateUpdateRedisPublisher {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    public CertificateUpdateRedisPublisher(RedisTemplate<String, Object> redisTemplate, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    public void publishCertificateStatusUpdate(String clientId, String certificateId, String status, String message) {
        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("clientId", clientId);
            eventData.put("certificateId", certificateId);
            eventData.put("status", status);
            eventData.put("message", message);
            eventData.put("timestamp", System.currentTimeMillis());
            
            String jsonData = objectMapper.writeValueAsString(eventData);
            redisTemplate.convertAndSend(SSE_REDIS_TOPIC, jsonData);
            log.info("Published certificate status update for certificateId: {}, status: {}", certificateId, status);
        } catch (JsonProcessingException e) {
            log.error("Error serializing certificate status update: {}", e.getMessage(), e);
        }
    }

    public void publishUpdate(String clientId, String certificateId) {
        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("clientId", clientId);
            eventData.put("certificateId", certificateId);
            eventData.put("timestamp", System.currentTimeMillis());

            String jsonData = objectMapper.writeValueAsString(eventData);
            redisTemplate.convertAndSend(SSE_REDIS_TOPIC, jsonData);
            log.info("Published certificate request update for certificateId: {}", certificateId);
        } catch (JsonProcessingException e) {
            log.error("Error serializing certificate request update: {}", e.getMessage(), e);
        }
    }

}