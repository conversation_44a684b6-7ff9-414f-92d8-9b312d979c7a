package com.concirrus.certificateservice.redis.pubsub;

import com.concirrus.certificateservice.sse.SseEmitterRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Redis subscriber for certificate status updates
 * Listens for messages on the Redis channel and forwards them to SSE clients
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisCertificateUpdateSubscriber implements MessageListener {

    private final SseEmitterRegistry sseEmitterRegistry;
    private final ObjectMapper objectMapper;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String msg = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("Received message from Redis: {}", msg);

        try {
            Map<String, Object> eventData = objectMapper.readValue(msg, Map.class);
            String clientId = (String) eventData.get("clientId");
            
            if (clientId == null) {
                log.warn("Received Redis message without clientId, cannot route to clients");
                return;
            }

            // Forward the event to clients with the same clientId
            // This is done asynchronously to avoid blocking the Redis message listener
            try {
                sseEmitterRegistry.sendToClient(clientId, eventData);
                log.info("Certificate status update sent to subscribers for clientId: {}", clientId);
            } catch (Exception e) {
                // Catch any exceptions to prevent them from bubbling up to the Redis message listener
                // This ensures the message is acknowledged even if there's an error sending to clients
                log.error("Error sending certificate update to clients: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("Error processing Redis message: {}", e.getMessage(), e);
        }
    }
}