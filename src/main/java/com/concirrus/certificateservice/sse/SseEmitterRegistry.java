package com.concirrus.certificateservice.sse;

import com.concirrus.certificateservice.redis.model.RetryMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Collections;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.concirrus.certificateservice.config.RedisConfig.SSE_REDIS_TOPIC;


@Component
@Slf4j
public class SseEmitterRegistry {

    // Map to store emitters based on clientId
    private final static Map<String, Set<SseEmitter>> emittersByClientId = new ConcurrentHashMap<>();
    // Nested map to store retry messages: clientId -> (certificateId -> RetryMessage)
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, RetryMessage>> retryCache = new ConcurrentHashMap<>();

    // Lock for synchronizing operations on emitters
    private final Lock emitterLock = new ReentrantLock();

    /**
     * Creates a new emitter for a specific clientId
     *
     * @param clientId The client identifier
     * @return A new SseEmitter instance
     */
    public SseEmitter createEmitter(String clientId) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);

        // Add the emitter to the set for the specific clientId
        emittersByClientId.computeIfAbsent(clientId, k -> new CopyOnWriteArraySet<>()).add(emitter);
        log.info("New SSE connection for clientId '{}'. Existing SSE connection count for this client: {}, Total active clients: {}",
                clientId,
                emittersByClientId.getOrDefault(clientId, Collections.emptySet()).size(),
                emittersByClientId.size());

        // Cleanup the emitter when the connection completes, times out, or encounters an error
        emitter.onCompletion(() -> {
            removeEmitter(clientId, emitter);
            log.info("SSE connection completed for client {}. Active clients: {}", clientId, emittersByClientId.size());
        });

        emitter.onTimeout(() -> {
            removeEmitter(clientId, emitter);
            log.info("SSE connection timed out for client {}. Active clients: {}", clientId, emittersByClientId.size());
        });

        emitter.onError((ex) -> {
            removeEmitter(clientId, emitter);
            log.error("SSE connection error for client {}: {}. Active clients: {}", clientId, ex.getMessage(), emittersByClientId.size());
        });

        // Send an initial dummy event to establish the connection quickly on the client side
        try {
            SseEmitter.SseEventBuilder event = SseEmitter.event()
                    .name("INIT")
                    .data("Connection established with server.")
                    .id(String.valueOf(System.currentTimeMillis()));

            emitter.send(event);
            log.info("Sent initial SSE event to clientId '{}'", clientId);
        } catch (Exception e) {
            removeEmitter(clientId, emitter);
            log.error("Failed to send initial SSE event to clientId '{}' : {}", clientId, e.getMessage());
        }

        return emitter;
    }

    /**
     * Removes an emitter from the registry
     *
     * @param clientId The client identifier
     * @param emitter The emitter to remove
     */
    private void removeEmitter(String clientId, SseEmitter emitter) {
        try {
            emitterLock.lock();
            Set<SseEmitter> emittersForClient = emittersByClientId.get(clientId);

            if (CollectionUtils.isEmpty(emittersForClient)) {
                emittersByClientId.remove(clientId);  // Remove clientId if no emitters are left
                log.info("Removed clientId '{}' from registry as it has no active emitters.", clientId);
            } else {
                boolean removed = emittersForClient.remove(emitter);
                if (removed) {
                    log.info("Removed emitter for clientId '{}'.", clientId);

                    // If this was the last emitter, clean up the client entry
                    if (emittersForClient.isEmpty()) {
                        emittersByClientId.remove(clientId);
                        log.info("Removed clientId '{}' from registry as it has no active emitters.", clientId);
                    }
                } else {
                    log.warn("Emitter not found for clientId '{}'.", clientId);
                }
            }
        } finally {
            emitterLock.unlock();
        }
    }

    /**
     * Sends an event to all emitters for a specific client
     *
     * @param clientId The client identifier
     * @param data The event data to send
     */
    public void sendToClient(String clientId, Object data) {
        Set<SseEmitter> emittersForClient;

        try {
            emitterLock.lock();
            emittersForClient = emittersByClientId.get(clientId);
        } finally {
            emitterLock.unlock();
        }

        if (CollectionUtils.isEmpty(emittersForClient)) {
            log.info("No active emitters found for clientId '{}'", clientId);
            storeForRetry(clientId, data);
            return;
        }

        log.info("Sending message to {} emitters for clientId '{}'", emittersForClient.size(), clientId);

        // Extract certificateId for retry cache
        String certificateId = extractCertificateId(data);
        log.info("Extracted certificateId : '{}'", certificateId);

        boolean allSent = true;
        Set<SseEmitter> deadEmitters = new CopyOnWriteArraySet<>();

        // Try to send to all emitters
        for (SseEmitter emitter : emittersForClient) {
            try {
                // Check if emitter is still valid before sending
                emitter.send(SseEmitter.event().name(SSE_REDIS_TOPIC).data(data));
            } catch (Exception ex) {
                log.error("Failed to send event to clientId '{}'. Reason: {}", clientId, ex.getMessage());
                allSent = false;
                deadEmitters.add(emitter);

                // Don't call complete() here as it might cause race conditions
                // Just mark for removal
            }
        }

        // Clean up dead emitters outside the iteration loop
        if (!deadEmitters.isEmpty()) {
            try {
                emitterLock.lock();
                for (SseEmitter deadEmitter : deadEmitters) {
                    try {
                        // Safe removal that won't affect concurrent operations
                        Set<SseEmitter> currentEmitters = emittersByClientId.get(clientId);
                        if (currentEmitters != null) {
                            currentEmitters.remove(deadEmitter);

                            // If this was the last emitter, clean up the client entry
                            if (currentEmitters.isEmpty()) {
                                emittersByClientId.remove(clientId);
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Error removing dead emitter: {}", e.getMessage());
                    }
                }
            } finally {
                emitterLock.unlock();
            }
        }

        // Store for retry if any sends failed
        if (!allSent) {
            storeForRetry(clientId, data);
        } else {
            // Update existing retry message with latest data if present
            updateRetryCache(clientId, certificateId, data);
        }
    }

    /**
     * Stores a message for retry
     *
     * @param clientId The client identifier
     * @param data The data to store
     */
    private void storeForRetry(String clientId, Object data) {
        String certificateId = extractCertificateId(data);
        log.warn("Storing message for retry for client {}, certificateId : {}", clientId, certificateId);
        retryCache.computeIfAbsent(clientId, k -> new ConcurrentHashMap<>())
                .put(certificateId, new RetryMessage(data, 1, System.currentTimeMillis() + 1000));
    }

    /**
     * Updates an existing retry message with the latest data
     *
     * @param clientId The client identifier
     * @param certificateId The certificate identifier
     * @param data The updated data
     */
    private void updateRetryCache(String clientId, String certificateId, Object data) {
        retryCache.computeIfPresent(clientId, (cid, clientMap) -> {
            clientMap.computeIfPresent(certificateId, (certId, existingRetry) -> {
                log.info("Updating retry cache with latest data for clientId '{}' and certificateId '{}'", clientId, certificateId);
                existingRetry.setData(data);
                return existingRetry;
            });
            return clientMap;
        });
    }

    /**
     * Sends an event to all clients
     *
     * @param data The event data to send
     */
    public void sendToAll(Object data) {
        for (Map.Entry<String, Set<SseEmitter>> entry : emittersByClientId.entrySet()) {
            String clientId = entry.getKey();
            sendToClient(clientId, data);
        }
    }

    /**
     * Sends keep-alive messages to all clients to prevent connection timeouts
     * Runs every 30 seconds
     */
    @Scheduled(fixedRate = 30000)
    public void sendKeepAliveToAllClients() {
        Map<String, Set<SseEmitter>> currentEmitters;

        try {
            emitterLock.lock();
            // Create a copy to avoid concurrent modification
            currentEmitters = new ConcurrentHashMap<>(emittersByClientId);
        } finally {
            emitterLock.unlock();
        }

        for (Map.Entry<String, Set<SseEmitter>> entry : currentEmitters.entrySet()) {
            String clientId = entry.getKey();
            Set<SseEmitter> emitters = entry.getValue();
            Set<SseEmitter> deadEmitters = new CopyOnWriteArraySet<>();

            for (SseEmitter emitter : emitters) {
                try {
                    // Send a comment as keep-alive which won't trigger event handlers on client
                    emitter.send(SseEmitter.event().comment("keep-alive"));
                } catch (Exception ex) {
                    deadEmitters.add(emitter);
                    log.error("Keep-alive failed for clientId '{}'. Reason: {}", clientId, ex.getMessage());
                }
            }

            // Clean up dead emitters
            if (!deadEmitters.isEmpty()) {
                try {
                    emitterLock.lock();
                    for (SseEmitter deadEmitter : deadEmitters) {
                        removeEmitter(clientId, deadEmitter);
                    }
                } finally {
                    emitterLock.unlock();
                }
            }
        }
    }

    /**
     * Retries sending failed messages
     * Runs every 5 seconds
     */
    @Scheduled(fixedDelay = 5000)
    public void retryFailedMessages() {
        long now = System.currentTimeMillis();

        for (Map.Entry<String, ConcurrentHashMap<String, RetryMessage>> clientEntry : retryCache.entrySet()) {
            String clientId = clientEntry.getKey();
            ConcurrentHashMap<String, RetryMessage> certificateMap = clientEntry.getValue();
            Iterator<Map.Entry<String, RetryMessage>> certificateIterator = certificateMap.entrySet().iterator();

            while (certificateIterator.hasNext()) {
                Map.Entry<String, RetryMessage> certificateEntry = certificateIterator.next();
                String certificateId = certificateEntry.getKey();
                RetryMessage msg = certificateEntry.getValue();

                if (msg.getNextRetryTime() > now) {
                    continue;  // Not time to retry yet
                }

                Set<SseEmitter> emitters;
                try {
                    emitterLock.lock();
                    emitters = emittersByClientId.get(clientId);
                } finally {
                    emitterLock.unlock();
                }

                boolean allSent = true;
                Set<SseEmitter> deadEmitters = new CopyOnWriteArraySet<>();

                if (!CollectionUtils.isEmpty(emitters)) {
                    for (SseEmitter emitter : emitters) {
                        try {
                            emitter.send(SseEmitter.event().name(SSE_REDIS_TOPIC).data(msg.getData()));
                        } catch (Exception ex) {
                            allSent = false;
                            deadEmitters.add(emitter);
                            log.warn("Retry failed for clientId '{}' certificateId '{}': {}",
                                    clientId, certificateId, ex.getMessage());
                        }
                    }

                    // Clean up dead emitters
                    if (!deadEmitters.isEmpty()) {
                        try {
                            emitterLock.lock();
                            for (SseEmitter deadEmitter : deadEmitters) {
                                removeEmitter(clientId, deadEmitter);
                            }
                        } finally {
                            emitterLock.unlock();
                        }
                    }
                } else {
                    allSent = false;  // No emitters to send to
                }

                if (allSent) {
                    certificateIterator.remove();  // Remove from retry cache after success
                    log.info("Successfully resent message for clientId '{}', certificateId '{}'",
                            clientId, certificateId);
                } else if (msg.getAttempts() >= 5) {
                    certificateIterator.remove();  // Remove after max attempts
                    log.warn("Dropping message for clientId '{}', certificateId '{}' after max retry attempts",
                            clientId, certificateId);
                } else {
                    // Exponential backoff for retries
                    msg.setAttempts(msg.getAttempts() + 1);
                    msg.setNextRetryTime(now + (long) Math.pow(2, msg.getAttempts()) * 1000);
                    log.info("Rescheduling retry attempt {} for clientId '{}', certificateId '{}'",
                            msg.getAttempts(), clientId, certificateId);
                }
            }

            // Clean up empty client entries
            if (certificateMap.isEmpty()) {
                retryCache.remove(clientId);  // Remove client entry if no more messages
            }
        }
    }

    /**
     * Extracts the certificate ID from the event data
     *
     * @param data The event data
     * @return The certificate ID or a default value if not found
     */
    @SuppressWarnings("unchecked")
    private String extractCertificateId(Object data) {
        try {
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                Object certificateId = dataMap.get("certificateId");
                if (certificateId != null) {
                    return certificateId.toString();
                }
            }
        } catch (Exception e) {
            log.warn("Failed to extract certificateId from data: {}", e.getMessage());
        }
        return "unknown-" + System.currentTimeMillis();
    }
}