package com.concirrus.certificateservice.dal.impl;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import com.concirrus.certificateservice.dal.CertificateDAL;
import com.concirrus.certificateservice.dto.CertificateCountResponse;
import com.concirrus.certificateservice.dto.CertificateFilterRequest;
import com.concirrus.certificateservice.model.exception.InvalidArgumentsException;
import com.concirrus.certificateservice.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;
import org.springframework.data.mongodb.core.query.Query;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.concirrus.certificateservice.config.MongoConfig.CERTIFICATE_INFO_MONGO_TEMPLATE;
import static com.concirrus.certificateservice.constant.DbConstants.*;
import static com.concirrus.certificateservice.constant.enums.CertificateStatus.ISSUED;

@Slf4j
@Repository
public class CertificateDALImpl implements CertificateDAL {

    private final MongoTemplate mongoTemplate;
    private static final String CERTIFICATE_INFO_MONGO_COLLECTION = "certificate_info";

    public CertificateDALImpl(@Qualifier(CERTIFICATE_INFO_MONGO_TEMPLATE) MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Document save(Document certificateRequest) {
        log.info("Saving certificate document for certificateId: {}", certificateRequest.get(CERTIFICATE_ID));
        return mongoTemplate.save(certificateRequest, CERTIFICATE_INFO_MONGO_COLLECTION);
    }

    @Override
    public Document getCertificateById(String certificateId, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(certificateId, clientId, userId));
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where(CERTIFICATE_ID).is(certificateId));

            // Make sure we're using the correct field names
            if (StringUtils.isNotEmpty(clientId)) {
                query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            }
            if (StringUtils.isNotEmpty(userId)) {
                query.addCriteria(Criteria.where(CREATED_BY).is(userId));
            }

            return mongoTemplate.findOne(query, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);
        } catch (Exception e) {
            log.error("Error while fetching certificate by id : {}", certificateId, e);
            throw new RuntimeException("Failed to find the document", e);
        }
    }

    @Override
    public Pair<Long, List<Document>> getAllCertificatesBy(CertificateFilterRequest certificateFilterRequest, PageRequest pageRequest, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(certificateFilterRequest, pageRequest, clientId, userId));
        try {
            Query query = new Query();
            if (StringUtils.isNotEmpty(clientId)) {
                query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            }
            if (StringUtils.isNotEmpty(userId)) {
                query.addCriteria(Criteria.where(CREATED_BY).is(userId));
            }
            if (CollectionUtils.isNotEmpty(certificateFilterRequest.getStatus())) {
                query.addCriteria(Criteria.where(STATUS).in(certificateFilterRequest.getStatus()));
            }
            if (certificateFilterRequest.getApprovalRequired() != null) {
                query.addCriteria(Criteria.where(APPROVAL_REQUIRED).is(certificateFilterRequest.getApprovalRequired()));
            }
            if (certificateFilterRequest.getChangesRequired() != null) {
                query.addCriteria(Criteria.where(CHANGES_REQUIRED).is(certificateFilterRequest.getChangesRequired()));
            }
            if (CollectionUtils.isNotEmpty(certificateFilterRequest.getApprovalStatus())) {
                query.addCriteria(Criteria.where(APPROVAL_STATUS).in(certificateFilterRequest.getApprovalStatus()));
            }
            if (CollectionUtils.isNotEmpty(certificateFilterRequest.getChangeRequestStatus())) {
                query.addCriteria(Criteria.where(CHANGE_REQUEST_STATUS).in(certificateFilterRequest.getChangeRequestStatus()));
            }
            query.addCriteria(Criteria.where(IS_DELETED).is(false));

            // Date range filtering for string dates
            if (StringUtils.isNotEmpty(certificateFilterRequest.getFromDate()) ||
                    StringUtils.isNotEmpty(certificateFilterRequest.getToDate())) {

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

                // Create a single criteria for createdAt with both conditions
                Criteria dateRangeCriteria = Criteria.where(CREATED_AT);

                if (StringUtils.isNotEmpty(certificateFilterRequest.getFromDate())) {
                    try {
                        LocalDate fromDate = LocalDate.parse(certificateFilterRequest.getFromDate(), formatter);
                        String fromDateStr = fromDate.toString(); // Format: YYYY-MM-DD
                        dateRangeCriteria = dateRangeCriteria.gte(fromDateStr);
                    } catch (DateTimeParseException e) {
                        log.error("Error parsing fromDate: {}", certificateFilterRequest.getFromDate(), e);
                        throw new InvalidArgumentsException("Invalid fromDate format. Use dd-MM-yyyy");
                    }
                }

                if (StringUtils.isNotEmpty(certificateFilterRequest.getToDate())) {
                    try {
                        LocalDate toDate = LocalDate.parse(certificateFilterRequest.getToDate(), formatter);
                        toDate = toDate.plusDays(1); // Include the end date
                        String toDateStr = toDate.toString(); // Format: YYYY-MM-DD
                        dateRangeCriteria = dateRangeCriteria.lt(toDateStr);
                    } catch (DateTimeParseException e) {
                        log.error("Error parsing toDate: {}", certificateFilterRequest.getToDate(), e);
                        throw new InvalidArgumentsException("Invalid toDate format. Use dd-MM-yyyy");
                    }
                }

                // Add the combined date criteria to the query
                query.addCriteria(dateRangeCriteria);
            }

            // Free text search across multiple fields
            if (StringUtils.isNotEmpty(certificateFilterRequest.getFreeText())) {
                String searchText = certificateFilterRequest.getFreeText().trim();

                // Create regex pattern for case-insensitive partial matching
                Pattern regexPattern = Pattern.compile(Pattern.quote(searchText), Pattern.CASE_INSENSITIVE);

                Criteria freeTextCriteria = new Criteria().orOperator(
                        Criteria.where(CERTIFICATE_ID).regex(regexPattern),
                        Criteria.where(CERTIFICATE_NAME).regex(regexPattern),
                        Criteria.where(CERTIFICATE_NUMBER).regex(regexPattern),
                        Criteria.where(POLICY_NUMBER).regex(regexPattern),
                        Criteria.where(CERTIFICATE_HOLDER_NAME).regex(regexPattern)
                );

                query.addCriteria(freeTextCriteria);
            }

            // Log the final query before execution
            log.info("MongoDB Query: {}", query.toString());

            // total count of items that matches the condition
            long totalCount = mongoTemplate.count(query, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);
            log.info("Total count before pagination: {}", totalCount);

            if (Objects.nonNull(pageRequest)) {
                // paginated Query
                Query paginatedQuery = getPaginationQueryForMongo(query, pageRequest);
                log.info("Paginated MongoDB Query: {}", paginatedQuery.toString());
                log.info("Skip: {}, Limit: {}, Sort: {}",
                        paginatedQuery.getSkip(),
                        paginatedQuery.getLimit(),
                        paginatedQuery.getSortObject().toJson());

                List<Document> certificateList = mongoTemplate.find(paginatedQuery, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);
                log.info("Fetched {} certificates for clientId: {}", certificateList.size(), clientId);
                return Pair.of(totalCount, certificateList);
            } else {
                List<Document> certificateList = mongoTemplate.find(query, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);
                log.info("Fetched {} certificates for clientId: {}", certificateList.size(), clientId);
                return Pair.of(totalCount, certificateList);
            }
        } catch (Exception e) {
            log.error("Error while fetching all certificates", e);
            throw new RuntimeException("Failed to find the document", e);
        }
    }

    @Override
    public Document updateCertificateById(String certificateId, Document updateCertificateRequest, String clientId, String userId) {
        log.info(LoggingUtils.logMethodEntry(certificateId, clientId, userId));
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where(CERTIFICATE_ID).is(certificateId));
            if (StringUtils.isNotEmpty(clientId)) {
                query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            }
            if (StringUtils.isNotEmpty(userId)) {
                query.addCriteria(Criteria.where(CREATED_BY).is(userId));
            }

            Document result = mongoTemplate.findAndReplace(query, updateCertificateRequest,
                    FindAndReplaceOptions.options().returnNew(),
                    CERTIFICATE_INFO_MONGO_COLLECTION);

            log.info("Certificate with id {} and document {} updated successfully", certificateId, result.get(MONGO_ID));
            return result;

        } catch (Exception e) {
            log.error("Error updating certificate with id: {}", certificateId, e);
            throw new RuntimeException("Failed to update certificate: " + certificateId, e);
        }
    }

    @Override
    public long getPreviousCertificatesCountByPolicyNumber(String policyNumber, String clientId) {
        log.info(LoggingUtils.logMethodEntry(policyNumber, clientId));
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where(POLICY_NUMBER).is(policyNumber));
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            query.addCriteria(Criteria.where(IS_DELETED).is(false));

            long count = mongoTemplate.count(query, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);
            log.info("Previous certificates count for policy number {}: {}", policyNumber, count);
            return count;
        } catch (Exception e) {
            log.error("Error while fetching previous certificates count for policy number: {}", policyNumber, e);
            throw new RuntimeException("Failed to find the document", e);
        }
    }

    @Override
    public long getCertificatesRequestsCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status) {
        log.info(LoggingUtils.logMethodEntry(clientId));
        try {
            Query totalQuery = new Query();
            if (StringUtils.isNotEmpty(clientId)) {
                totalQuery.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            }
            if (StringUtils.isNotEmpty(userId)) {
                totalQuery.addCriteria(Criteria.where(CREATED_BY).is(userId));
            }
            if (CollectionUtils.isNotEmpty(status)) {
                totalQuery.addCriteria(Criteria.where(STATUS).in(status));
            }

            // Create separate criteria for approval and change request conditions
            Criteria approvalCriteria = null;
            Criteria changeRequestCriteria = null;

            // Build approval criteria if either approvalRequired or approvalStatus is provided
            if (approvalRequired != null || CollectionUtils.isNotEmpty(approvalStatus)) {
                List<Criteria> approvalConditions = new ArrayList<>();

                if (approvalRequired != null) {
                    approvalConditions.add(Criteria.where(APPROVAL_REQUIRED).is(approvalRequired));
                }

                if (CollectionUtils.isNotEmpty(approvalStatus)) {
                    approvalConditions.add(Criteria.where(APPROVAL_STATUS).in(approvalStatus));
                }

                // If multiple approval conditions, combine with AND
                if (approvalConditions.size() > 1) {
                    approvalCriteria = new Criteria().andOperator(approvalConditions.toArray(new Criteria[0]));
                } else if (approvalConditions.size() == 1) {
                    approvalCriteria = approvalConditions.get(0);
                }
            }

            // Build change request criteria if either changesRequired or changeRequestStatus is provided
            if (changesRequired != null || CollectionUtils.isNotEmpty(changeRequestStatus)) {
                List<Criteria> changeRequestConditions = new ArrayList<>();

                if (changesRequired != null) {
                    changeRequestConditions.add(Criteria.where(CHANGES_REQUIRED).is(changesRequired));
                }

                if (CollectionUtils.isNotEmpty(changeRequestStatus)) {
                    changeRequestConditions.add(Criteria.where(CHANGE_REQUEST_STATUS).in(changeRequestStatus));
                }

                // If multiple change request conditions, combine with AND
                if (changeRequestConditions.size() > 1) {
                    changeRequestCriteria = new Criteria().andOperator(changeRequestConditions.toArray(new Criteria[0]));
                } else if (changeRequestConditions.size() == 1) {
                    changeRequestCriteria = changeRequestConditions.get(0);
                }
            }

            // If both approval and change request criteria exist, combine with OR
            if (approvalCriteria != null && changeRequestCriteria != null) {
                totalQuery.addCriteria(new Criteria().orOperator(approvalCriteria, changeRequestCriteria));
            } else {
                // Add individual criteria if only one exists
                if (approvalCriteria != null) {
                    totalQuery.addCriteria(approvalCriteria);
                }
                if (changeRequestCriteria != null) {
                    totalQuery.addCriteria(changeRequestCriteria);
                }
            }
            totalQuery.addCriteria(Criteria.where(IS_DELETED).is(false));
            long totalCount = mongoTemplate.count(totalQuery, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);
            log.info("Total referred certificates count for clientId {}: {}", clientId, totalCount);
            return totalCount;
        } catch (Exception e) {
            log.error("Error while counting referred certificates", e);
            throw new RuntimeException("Failed to count referred certificates", e);
        }
    }

    /**
     * Creates a MongoDB Query object for pagination based on the provided PageRequest.
     *
     * @param req The PageRequest containing pagination details.
     * @return A Query object with skip, limit, and sort parameters for MongoDB pagination.
     */
    public static Query getPaginationQueryForMongo(Query query, PageRequest req) {
        final int limit = req.getPageSize();
        final int offset = req.getPageNumber() * req.getPageSize();

        Sort sortOrder = req.getSort();

        if (Objects.isNull(query)) {
            query = new Query();
        }
        query.skip(offset).limit(limit);
        query.with(sortOrder);

        log.info("The pagination query is limit:{}, offset:{}, sort:{}", query.getLimit(), query.getSkip(), query.getSortObject());
        return query;
    }

    @Override
    public CertificateCountResponse getCertificatesRequestsDetailedCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status) {
        log.info(LoggingUtils.logMethodEntry(clientId));
        try {
            // Get total count using existing method
            long totalCount = getCertificatesRequestsCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, userId, status);

            // Build base criteria list for common conditions
            List<Criteria> baseCriteria = new ArrayList<>();
            if (StringUtils.isNotEmpty(clientId)) {
                baseCriteria.add(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            }
            if (StringUtils.isNotEmpty(userId)) {
                baseCriteria.add(Criteria.where(CREATED_BY).is(userId));
            }
            if (CollectionUtils.isNotEmpty(status)) {
                baseCriteria.add(Criteria.where(STATUS).in(status));
            }
            baseCriteria.add(Criteria.where(IS_DELETED).is(false));

            // Query for approval requests count (approvalRequired = true AND approvalStatus = PENDING)
            Query approvalQuery = new Query();
            for (Criteria criteria : baseCriteria) {
                approvalQuery.addCriteria(criteria);
            }
            approvalQuery.addCriteria(Criteria.where(APPROVAL_REQUIRED).is(true));
            approvalQuery.addCriteria(Criteria.where(APPROVAL_STATUS).is(CertificateStatus.PENDING.getValue()));
            long approvalRequestsCount = mongoTemplate.count(approvalQuery, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);

            // Query for change requests count (changesRequired = true AND changeRequestStatus = PENDING)
            Query changeRequestQuery = new Query();
            for (Criteria criteria : baseCriteria) {
                changeRequestQuery.addCriteria(criteria);
            }
            changeRequestQuery.addCriteria(Criteria.where(CHANGES_REQUIRED).is(true));
            changeRequestQuery.addCriteria(Criteria.where(CHANGE_REQUEST_STATUS).is(CertificateStatus.PENDING.getValue()));
            long changeRequestsCount = mongoTemplate.count(changeRequestQuery, Document.class, CERTIFICATE_INFO_MONGO_COLLECTION);

            log.info("Certificate counts for clientId {}: total={}, approvalRequests={}, changeRequests={}",
                    clientId, totalCount, approvalRequestsCount, changeRequestsCount);

            return CertificateCountResponse.builder()
                    .totalCount(totalCount)
                    .approvalRequestsCount(approvalRequestsCount)
                    .changeRequestsCount(changeRequestsCount)
                    .build();

        } catch (Exception e) {
            log.error("Error while getting detailed certificate counts", e);
            throw new RuntimeException("Failed to get detailed certificate counts", e);
        }
    }
}
