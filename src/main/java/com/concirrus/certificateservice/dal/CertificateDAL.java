package com.concirrus.certificateservice.dal;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import com.concirrus.certificateservice.dto.CertificateCountResponse;
import com.concirrus.certificateservice.dto.CertificateFilterRequest;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface CertificateDAL {
    Document save(Document document);
    Document getCertificateById(String certificateId, String clientId, String userId);
    Pair<Long, List<Document>> getAllCertificatesBy(CertificateFilterRequest certificateFilterRequest, PageRequest pageRequest, String clientId, String userId);
    Document updateCertificateById(String certificateId, Document certificateRequest, String clientId, String userId);
    long getPreviousCertificatesCountByPolicyNumber(String policyNumber, String clientId);
    long getCertificatesRequestsCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status);
    CertificateCountResponse getCertificatesRequestsDetailedCount(String clientId, Boolean approvalRequired, List<CertificateStatus> approvalStatus, Boolean changesRequired, List<CertificateStatus> changeRequestStatus, String userId, List<CertificateStatus> status);
}
