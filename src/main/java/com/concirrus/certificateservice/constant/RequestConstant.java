package com.concirrus.certificateservice.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RequestConstant {
    public static final String CLIENT_ID = "client-id";
    public static final String USER_ID = "x-user-id";
    public static final String LINE_OF_BUSINESS = "line-of-business";
    public static final String REALM_HEADER = "X-Realm";
    public static final String X_TENANT_ID = "x-tenant-id";
    public static final String SUBMISSION_ADMIN = "SUBMISSION_ADMIN";
    public static final String UNDERWRITER = "UNDERWRITER";
    public static final String USER_ROLES = "x-user-roles";
    public static final String ZERO = "0";
    public static final String HUNDRED = "100";
    public static final String ASC = "ASC";
    public static final String SUMMARY = "summary";
    public static final String APPLIED_LOGISTICS = "APPLIED_LOGISTICS";
}
