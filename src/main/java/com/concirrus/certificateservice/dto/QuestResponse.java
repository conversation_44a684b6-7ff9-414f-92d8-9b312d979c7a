package com.concirrus.certificateservice.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

public class QuestResponse implements Serializable {

    private static final long serialVersionUID = 7789284253047806745L;
    private Object data;
    private boolean success = true;
    private String message = SUCCESS_MESSAGE;
    public static final String DATE_JSON_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZ";

    private static final String SUCCESS_MESSAGE = "Request Processed Successfully";

    @JsonFormat(pattern = DATE_JSON_FORMAT)
    private Date timestamp = new Date();

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        if (StringUtils.isNotBlank(message))
            message = message.replaceAll("(\\w+\\.+)", "");
        this.message = message;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return String.format("QuestResponse{data=%s, success=%s, detail='%s', timestamp=%s}",
                data, success, message, timestamp);
    }
}
