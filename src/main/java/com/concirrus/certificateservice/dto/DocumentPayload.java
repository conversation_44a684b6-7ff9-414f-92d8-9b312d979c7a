package com.concirrus.certificateservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentPayload {
    private String eventId;
    private String eventType;
    private String documentName;
    private String name;
    private Object payload;
    private String clientId;
    private String submissionId;
    private String typeConversion;
    private Boolean isSubmissionDisabled;
}
