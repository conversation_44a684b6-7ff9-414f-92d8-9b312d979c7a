package com.concirrus.certificateservice.dto;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CertificateFilterRequest {
    private List<CertificateStatus> status;
    @Pattern(regexp = "^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\\d{4}$",
            message = "fromDate must be in dd-MM-yyyy format")
    private String fromDate; // dd-MM-yyyy format

    @Pattern(regexp = "^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\\d{4}$",
            message = "toDate must be in dd-MM-yyyy format")
    private String toDate; // dd-MM-yyyy format
    private String freeText; // For search across fields
    private Boolean approvalRequired;
    private List<CertificateStatus> approvalStatus;
    private Boolean changesRequired;
    private List<CertificateStatus> changeRequestStatus;
}