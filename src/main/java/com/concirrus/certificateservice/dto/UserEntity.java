package com.concirrus.certificateservice.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserEntity {
    private String id;
    private String keycloakId;
    private String tenantId;
    private String firstName;
    private String lastName;
    private String fullName;
    private String email;
    private String[] roles;
    private String status;
    private String createdBy;
    private String updatedBy;
    private Map<String, Object> additionalInfo;
    private Boolean active;
    private Boolean visibleOnUi;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Timestamp updatedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Timestamp createdAt;
    
    private Boolean totpEnabled;
    private Boolean ssoEnabled;
}