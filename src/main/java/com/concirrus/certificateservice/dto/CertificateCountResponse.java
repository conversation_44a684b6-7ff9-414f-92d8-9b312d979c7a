package com.concirrus.certificateservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * DTO for certificate count response containing detailed count information
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CertificateCountResponse implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * Total count of all certificate requests
     */
    private long totalCount;
    
    /**
     * Count of requests requiring approval (approvalRequired = true AND approvalStatus = PENDING)
     */
    private long approvalRequestsCount;
    
    /**
     * Count of requests requiring changes (changesRequired = true AND changeRequestStatus = PENDING)
     */
    private long changeRequestsCount;
}
