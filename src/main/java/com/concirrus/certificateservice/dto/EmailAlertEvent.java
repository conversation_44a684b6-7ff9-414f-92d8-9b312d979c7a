package com.concirrus.certificateservice.dto;

import com.concirrus.certificateservice.constant.enums.AlertMedium;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class EmailAlertEvent {
    private String clientId;
    private AlertMedium medium;
    private List<String> receivers;
    private String referenceId;
    private String sender;
    private Object content;
    private Instant sentAt;
    private Map<String, byte[]> attachments;
    private String subject;
    private JsonNode templateArgs;
    private String templateId;
    private String lob;
}
