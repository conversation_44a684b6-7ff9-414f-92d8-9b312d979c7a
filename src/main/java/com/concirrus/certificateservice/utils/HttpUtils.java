package com.concirrus.certificateservice.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import static com.concirrus.certificateservice.constant.RequestConstant.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HttpUtils {

    public static HttpEntity<HttpHeaders> getTenantHeaders(String clientId, String userId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(USER_ID, userId);
        headers.add(CLIENT_ID, clientId);
        return new HttpEntity<>(headers);
    }
}
