package com.concirrus.certificateservice.utils;

import com.concirrus.certificateservice.constant.enums.AlertMedium;
import com.concirrus.certificateservice.dto.DocumentPayload;
import com.concirrus.certificateservice.dto.EmailAlertEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.text.NumberFormat;
import java.util.Locale;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.concirrus.certificateservice.constant.CommonConstants.*;
import static com.concirrus.certificateservice.constant.DbConstants.*;


@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PayloadGenerator {
    public static String generateCertificatePayload(String clientId, Document certificateRequest) throws Exception {
        Map<String, Object> certificatePayload = new HashMap<>();
        certificatePayload.put(CERTIFICATE_ID, certificateRequest.get(CERTIFICATE_ID));
        certificatePayload.put(ISSUE_DATE, LocalDate.parse(certificateRequest.getString(GENERATED_AT), DateTimeFormatter.ofPattern("MM-dd-yyyy, HH:mm")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        certificatePayload.put(DOCUMENT_ID, certificateRequest.get(DOCUMENT_ID));
        certificatePayload.put(CERTIFICATE_NUMBER, certificateRequest.get(CERTIFICATE_NUMBER));
        certificatePayload.put(CERTIFICATE_NAME, certificateRequest.get(CERTIFICATE_NAME));
        certificatePayload.put(POLICY_NUMBER, certificateRequest.get(POLICY_NUMBER));
        certificatePayload.put(CERTIFICATE_HOLDER_NAME, certificateRequest.get(CERTIFICATE_HOLDER_NAME));
        certificatePayload.put(INSURABLE_VALUE, certificateRequest.get(INSURABLE_VALUE));
        certificatePayload.put(COVERAGE, certificateRequest.get(COVERAGE));
        certificatePayload.put(CONVEYANCE, certificateRequest.get(CONVEYANCE));
        certificatePayload.put(CURRENCY, certificateRequest.get(CURRENCY));
        certificatePayload.put(COMPANY_NAME, certificateRequest.get(COMPANY_NAME));
        certificatePayload.put(SHIPPING, certificateRequest.get(SHIPPING));
        certificatePayload.put(CARRIER_NAME, certificateRequest.get(CARRIER));
        certificatePayload.put(VESSEL_NAME, certificateRequest.get(VESSEL));
        certificatePayload.put(DEPARTURE_DATE, certificateRequest.get(DEPARTURE_DATE));
        certificatePayload.put(ORIGIN, certificateRequest.get(ORIGIN));
        // Get conveyance value once and handle null safety
        Object conveyanceValue = certificateRequest.get(CONVEYANCE);
        boolean isTruckOrRail = TRUCK_OR_RAIL.equals(conveyanceValue);
        // Set loading port based on conveyance type
        if (isTruckOrRail) {
            certificatePayload.put(LOADING_PORT, certificateRequest.get(LOADING_PORT_FOR_LAND_CONVEYANCE));
            certificatePayload.put(UNLOADING_PORT, certificateRequest.get(UNLOADING_PORT_FOR_LAND_CONVEYANCE));
        } else {
            certificatePayload.put(LOADING_PORT, certificateRequest.get(LOADING_PORT));
            certificatePayload.put(UNLOADING_PORT, certificateRequest.get(UNLOADING_PORT));
        }
        certificatePayload.put(DESTINATION, certificateRequest.get(DESTINATION));
        certificatePayload.put(COMMODITY_TYPE, certificateRequest.get(COMMODITY_TYPE));
        certificatePayload.put(MARK_NUMBER, certificateRequest.get(MARK_NUMBER));
        certificatePayload.put(COMMODITY_DESCRIPTION, certificateRequest.get(COMMODITY_DESCRIPTION));
        certificatePayload.put(COMMODITY_TERMS_AND_CONDITION, certificateRequest.get(COMMODITY_TERMS_AND_CONDITION));
        certificatePayload.put(COMMODITY_DEDUCTIBLE, formatCurrency(certificateRequest.get(COMMODITY_DEDUCTIBLE)));

        return createDocumentPayload(certificatePayload, clientId, certificateRequest);
    }

    private static String createDocumentPayload(Map<String, Object> certificatePayload, String clientId, Document certificateRequest) throws Exception {
        try {
            DocumentPayload documentPayloadDTO = new DocumentPayload(
                    UUID.randomUUID().toString(),
                    CREATE,
                    null,
                    CERTIFICATE_PDF,
                    certificatePayload,
                    clientId,
                    (String) certificateRequest.get(CERTIFICATE_ID),
                    PDF_EXTENSION,
                    false
            );

            ObjectMapper objectMapper = new ObjectMapper();
            log.info("Document Payload: " + objectMapper.writeValueAsString(documentPayloadDTO));
            return objectMapper.writeValueAsString(documentPayloadDTO);
        } catch (Exception e) {
            log.error("Error while constructing document payload: " + e.getMessage());
            throw new Exception("Error while constructing document payload: " + e.getMessage());
        }
    }


    public static String generateCertificateStatusEmailPayload(String name, String email, String template, String clientId, String lob) throws JsonProcessingException {
        EmailAlertEvent emailAlertEvent = new EmailAlertEvent();
        emailAlertEvent.setClientId(clientId);
        emailAlertEvent.setMedium(AlertMedium.EMAIL);
        emailAlertEvent.setReceivers(List.of(email));
        emailAlertEvent.setTemplateId(template);
        emailAlertEvent.setLob(lob);

        // Create template args as JsonNode
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode templateArgs = objectMapper.createObjectNode();
        templateArgs.put(RECEIVER_NAME, name);
        emailAlertEvent.setTemplateArgs(templateArgs);

        // Convert to JSON string
        return objectMapper.writeValueAsString(emailAlertEvent);
    }

    // Static formatter instance for better performance (thread-safe)
    private static final NumberFormat CURRENCY_FORMATTER = NumberFormat.getCurrencyInstance(Locale.US);

    /**
     * Formats a numeric value to American currency format.
     * Examples: 100000 -> $100,000, 0 -> $0, null -> $0
     *
     * @param value The numeric value to format (can be Number, String, or null)
     * @return Formatted currency string in American format
     */
    private static String formatCurrency(Object value) {
        if (value == null) {
            return "$0";
        }
        try {
            if (value instanceof Number) {
                double numericValue = ((Number) value).doubleValue();
                return numericValue == 0 ? "$0" : CURRENCY_FORMATTER.format(numericValue);
            } else if (value instanceof String) {
                String trimmed = ((String) value).trim();
                if (trimmed.isEmpty()) {
                    return "$0";
                }
                try {
                    double numericValue = Double.parseDouble(trimmed);
                    return numericValue == 0 ? "$0" : CURRENCY_FORMATTER.format(numericValue);
                } catch (NumberFormatException e) {
                    return trimmed;
                }
            } else {
                log.warn("Unsupported value type '{}' for currency formatting, returning string representation", value.getClass().getSimpleName());
                return value.toString();
            }
        } catch (Exception e) {
            log.warn("Unexpected error formatting currency for value '{}', returning as string", value, e);
            return value.toString();
        }
    }
}
