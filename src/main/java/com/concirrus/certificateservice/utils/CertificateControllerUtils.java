package com.concirrus.certificateservice.utils;

import com.concirrus.certificateservice.dto.PagedResponse;
import com.concirrus.certificateservice.model.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.concirrus.certificateservice.constant.CommonConstants.COMMA;
import static com.concirrus.certificateservice.constant.RequestConstant.SUBMISSION_ADMIN;
import static com.concirrus.certificateservice.constant.RequestConstant.UNDERWRITER;

/**
 * <AUTHOR> mudit
 * @since : June 2025
 * * Utility class for certificate controller operations
 */
@Slf4j
public class CertificateControllerUtils {

    private CertificateControllerUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Parse user roles from comma-separated string
     * 
     * @param userRoles Comma-separated user roles
     * @return Set of user roles
     */
    public static Set<String> parseUserRoles(String userRoles) {
        return StringUtils.isBlank(userRoles) ? 
                Collections.emptySet() : 
                Stream.of(userRoles.split(COMMA)).collect(Collectors.toSet());
    }
    
    /**
     * Check if user has admin role
     * 
     * @param userRoles Set of user roles
     * @return true if user has admin role, false otherwise
     */
    public static boolean hasAdminRole(Set<String> userRoles) {
        return userRoles.contains(SUBMISSION_ADMIN) || userRoles.contains(UNDERWRITER);
    }
    
    /**
     * Validate that user has admin role, throw exception if not
     * 
     * @param userRoles Set of user roles
     * @throws BadRequestException if user doesn't have admin role
     */
    public static void validateAdminRole(Set<String> userRoles) {
        if (!hasAdminRole(userRoles)) {
            String errorMessage = "Only SUBMISSION_ADMIN or UNDERWRITER can update certificate status via an API";
            log.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }
    }
    
    /**
     * Create a PagedResponse with the given data
     * 
     * @param data Data to include in the response
     * @return PagedResponse containing the data
     */
    public static PagedResponse createPagedResponse(Object data) {
        PagedResponse pagedResponse = new PagedResponse();
        pagedResponse.setData(data);
        return pagedResponse;
    }
}