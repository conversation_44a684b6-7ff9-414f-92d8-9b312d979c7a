package com.concirrus.certificateservice.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;


/**
 * Exception thrown when access to a resource is denied due to insufficient permissions.
 *
 * <p>This exception is mapped to HTTP status code 403 Forbidden.</p>
 */
@ResponseStatus(value= HttpStatus.FORBIDDEN, reason="Insufficient Access.")
public class AccessDeniedException extends RuntimeException {

    /**
     * Constructs a new AccessDeniedException with a default message.
     */
    public AccessDeniedException() {
        super("Insufficient Access.");
    }

    /**
     * Constructs a new AccessDeniedException with a specified message.
     *
     * @param message the detail message
     */
    public AccessDeniedException(String message) {
        super(message);
    }
}
