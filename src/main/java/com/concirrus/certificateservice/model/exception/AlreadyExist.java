package com.concirrus.certificateservice.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mudit
 * @since : June 2025
 */
@ResponseStatus(value = HttpStatus.CONFLICT, reason = "Already exist")
public class AlreadyExist extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -674505462563241978L;
    private static final String DEFAULT_MESSAGE = "Resource already exist ";

    public AlreadyExist(String resourceId) {
        super(DEFAULT_MESSAGE + " : " + resourceId);
    }

    public AlreadyExist(String message, String resourceId) {
        super(message + " - " + resourceId);
    }

    public AlreadyExist() {
        super(DEFAULT_MESSAGE);
    }

    public AlreadyExist(Throwable cause) {
        super(cause);
    }
}
