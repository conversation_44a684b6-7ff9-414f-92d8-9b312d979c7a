package com.concirrus.certificateservice.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mudit
 * @since : June 2025
 */
@ResponseStatus(value = HttpStatus.TOO_EARLY)
public class TooEarlyRequestException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 3749138303937568591L;

    private static final String DEFAULT_MESSAGE = "Too early request, Please wait for the appropriate time to send the request again.";

    public TooEarlyRequestException() {
        super(DEFAULT_MESSAGE);
    }

    public TooEarlyRequestException(String message) {
        super(message);
    }
}
