package com.concirrus.certificateservice.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mudit
 * @since : June 2025
 */
@ResponseStatus(value = HttpStatus.NOT_ACCEPTABLE)
public class InvalidArgumentsException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -5411286393483389398L;
    private static final String DEFAULT_MESSAGE = "Invalid arguments";

    public InvalidArgumentsException(String message) {
        super(message);
    }

    public InvalidArgumentsException() {
        super(DEFAULT_MESSAGE);
    }

    public InvalidArgumentsException(Throwable cause) {
        super(cause);
    }

}
