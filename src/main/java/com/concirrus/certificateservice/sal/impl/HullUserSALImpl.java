package com.concirrus.certificateservice.sal.impl;

import com.concirrus.certificateservice.dto.QuestResponse;
import com.concirrus.certificateservice.dto.UserEntity;
import com.concirrus.certificateservice.sal.HullUserSAL;
import com.concirrus.certificateservice.utils.HttpUtils;
import com.concirrus.certificateservice.utils.LoggingUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import static com.concirrus.certificateservice.constant.RequestConstant.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class HullUserSALImpl implements HullUserSAL {

    @Value("${realm.name.cip}")
    private String cipRealmName;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Override
    public UserEntity getUserByKeycloakId(String userId, boolean summary, String clientId) {
        log.info(LoggingUtils.logMethodEntry(userId, summary, clientId));
        try {
            String url = UriComponentsBuilder.fromHttpUrl("http://marine-user-service/v2/internal/" + userId)
                    .queryParam(SUMMARY, summary)
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.addAll(HttpUtils.getTenantHeaders(clientId, userId).getHeaders());
            headers.add(LINE_OF_BUSINESS, APPLIED_LOGISTICS);
            headers.add(REALM_HEADER, cipRealmName);
            headers.add(X_TENANT_ID, clientId);

            HttpEntity<?> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<QuestResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    QuestResponse.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                QuestResponse questResponse = response.getBody();
                if (questResponse.getData() != null) {
                    UserEntity user = objectMapper.convertValue(questResponse.getData(), UserEntity.class);
                    log.info("Retrieved user by Keycloak ID: {}", userId);
                    return user;
                }
            }

            log.warn("Failed to get user by Keycloak ID: {}. Status: {}", userId, response.getStatusCode());
            return null;

        } catch (HttpClientErrorException.NotFound ex) {
            log.warn("User with Keycloak ID {} not found", userId);
            return null;
        } catch (Exception ex) {
            log.error("Error retrieving user by Keycloak ID {}: {}", userId, ex.getMessage(), ex);
            return null;
        } finally {
            log.info(LoggingUtils.logMethodExit());
        }
    }
}
