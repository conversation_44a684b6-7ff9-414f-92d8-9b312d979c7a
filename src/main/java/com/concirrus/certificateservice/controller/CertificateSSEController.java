package com.concirrus.certificateservice.controller;

import com.concirrus.certificateservice.sse.SseEmitterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Slf4j
@RestController
@RequestMapping("/certificate/sse")
@RequiredArgsConstructor
public class CertificateSSEController {

    private final SseEmitterRegistry sseEmitterRegistry;

    @GetMapping("/subscribe/{clientId}")
    public SseEmitter subscribe(@PathVariable String clientId) {
        log.info("New SSE subscription request for clientId: {}", clientId);
        return sseEmitterRegistry.createEmitter(clientId);
    }
}