package com.concirrus.certificateservice.controller;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import com.concirrus.certificateservice.dto.CertificateCountResponse;
import com.concirrus.certificateservice.dto.CertificateFilterRequest;
import com.concirrus.certificateservice.dto.PagedResponse;
import com.concirrus.certificateservice.model.exception.BadRequestException;
import com.concirrus.certificateservice.service.CertificateService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Set;

import static com.concirrus.certificateservice.constant.DbConstants.*;
import static com.concirrus.certificateservice.constant.RequestConstant.*;
import static com.concirrus.certificateservice.utils.CertificateControllerUtils.*;

@Slf4j
@Validated
@RestController
@RequestMapping("/certificate")
@RequiredArgsConstructor
public class CertificateController {

    private final CertificateService certificateService;

    @PostMapping
    public PagedResponse createCertificate(@RequestBody Document certificateRequest,
                                           @RequestHeader(value = CLIENT_ID) String clientId,
                                           @RequestHeader(USER_ID) String userId) {
        return createPagedResponse(certificateService.createCertificate(certificateRequest, clientId, userId));
    }

    @PutMapping("/{certificateId}")
    public PagedResponse updateCertificate(@PathVariable("certificateId") String certificateId,
                                           @RequestBody Document certificateRequest,
                                           @RequestHeader(value = CLIENT_ID) String clientId,
                                           @RequestHeader(USER_ID) String userId) {
        return createPagedResponse(certificateService.updateCertificate(certificateId, certificateRequest, clientId, userId));
    }

    @PatchMapping
    public PagedResponse createOrUpdateCertificate(@RequestBody Document certificateRequest,
                                                   @RequestHeader(value = CLIENT_ID) String clientId,
                                                   @RequestHeader(USER_ID) String userId) {
        return createPagedResponse(certificateService.createOrUpdateCertificate(certificateRequest, clientId, userId));
    }

    @GetMapping("/{certificateId}")
    public PagedResponse getCertificateById(@PathVariable("certificateId") String certificateId,
                                        @RequestHeader(value = CLIENT_ID) String clientId,
                                        @RequestHeader(USER_ID) String userId,
                                        @RequestHeader(value = USER_ROLES) String userRoles) {
        Set<String> userRoleSet = parseUserRoles(userRoles);
        String effectiveUserId = hasAdminRole(userRoleSet) ? null : userId;
        return createPagedResponse(certificateService.getCertificateById(certificateId, clientId, effectiveUserId));
    }

    @PostMapping("/search")
    public PagedResponse getAllCertificates(@Valid @RequestBody CertificateFilterRequest certificateFilterRequest,
                                            @RequestParam(defaultValue = ZERO) Integer pageNumber,
                                            @RequestParam(defaultValue = HUNDRED) Integer pageSize,
                                            @RequestParam(defaultValue = ASC) Sort.Direction sortOrder,
                                            @RequestParam(defaultValue = CREATED_AT) String sortBy,
                                            @RequestHeader(value = CLIENT_ID) String clientId,
                                            @RequestHeader(USER_ID) String userId,
                                            @RequestHeader(value = USER_ROLES) String userRoles) {
        try {
            PageRequest page = PageRequest.of(pageNumber, pageSize, Sort.by(sortOrder, sortBy));
            Set<String> userRoleSet = parseUserRoles(userRoles);
            String effectiveUserId = hasAdminRole(userRoleSet) ? null : userId;

            Pair<Long, List<Document>> certificatesData = certificateService.getAllCertificates(certificateFilterRequest, page, clientId, effectiveUserId);

            PagedResponse pagedResponse = new PagedResponse();
            pagedResponse.setData(certificatesData.getRight());
            pagedResponse.setTotalItems(certificatesData.getLeft());
            return pagedResponse;
        } catch (DateTimeParseException e) {
            log.error("Date parsing error: {}", e.getMessage());
            throw new BadRequestException("Invalid date format. Please use dd-MM-yyyy format.");
        }
    }

    @PostMapping("/send-email")
    public void sendCertificateEmail(@RequestParam(value = EMAIL) String email,
                                     @RequestParam(value = TEMPLATE) String template,
                                     @RequestParam(value = NAME) String name,
                                     @RequestParam(value = LOB) String lob,
                                     @RequestHeader(value = CLIENT_ID) String clientId) {
        certificateService.sendCertificateEmail(name,email, template, clientId, lob);
    }

    @GetMapping("/requests/count")
    public PagedResponse getCertificatesRequestsCount(@RequestParam(required = false) List<CertificateStatus> status,
                                                      @RequestParam(required = false) Boolean approvalRequired,
                                                      @RequestParam(required = false) List<CertificateStatus> approvalStatus,
                                                      @RequestParam(required = false) List<CertificateStatus> changeRequestStatus,
                                                      @RequestParam(required = false) Boolean changesRequired,
                                                      @RequestHeader(value = CLIENT_ID) String clientId,
                                                      @RequestHeader(USER_ID) String userId,
                                                      @RequestHeader(value = USER_ROLES) String userRoles) {
        Set<String> userRoleSet = parseUserRoles(userRoles);
        String effectiveUserId = hasAdminRole(userRoleSet) ? null : userId;

        CertificateCountResponse countsResponse = certificateService.getCertificatesRequestsDetailedCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, effectiveUserId, status);

        return createPagedResponse(countsResponse);
    }

    @PostMapping("/requests/status-update/{certificateId}")
    public PagedResponse updateCertificateStatus(@PathVariable("certificateId") String certificateId,
                                                 @RequestBody Document certificateRequest,
                                                 @RequestParam(required = false) CertificateStatus approvalStatus,
                                                 @RequestParam(required = false) CertificateStatus changeRequestStatus,
                                                 @RequestHeader(value = CLIENT_ID) String clientId,
                                                 @RequestHeader(USER_ID) String userId,
                                                 @RequestHeader(value = USER_ROLES) String userRoles) {
        Set<String> userRoleSet = parseUserRoles(userRoles);
        validateAdminRole(userRoleSet);

        Document response = certificateService.updateCertificateStatus(certificateId, certificateRequest, approvalStatus, changeRequestStatus, clientId, userId);

        return createPagedResponse(response);
    }
}
