package com.concirrus.certificateservice.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

/**
 * Mongo Config for certificate DB
 *
 * <AUTHOR>
 * @since June-2025
 */
@Slf4j
@Configuration
public class MongoConfig {

    public static final String CERTIFICATE_INFO_MONGO_TEMPLATE = "certificateInfoMongoTemplate";

    @Value("${mongo.certificate.db}")
    private String database;

    @Value("${mongo.policy.uri}")
    private String uri;

    @Bean(name = CERTIFICATE_INFO_MONGO_TEMPLATE)
    @Primary
    public MongoTemplate getMongoTemplate() {
        log.info("Creating mongoTemplate by URI :{} and dbName :{}", uri, database);
        return new MongoTemplate(getMongoFactory(uri, database));
    }

    /**
     * Creates a MongoDB database factory based on the provided MongoDB connection URI and database name.
     *
     * @param uri    The connection URI for MongoDB.
     * @param dbName The name of the MongoDB database.
     * @return A MongoDatabaseFactory instance based on the provided parameters.
     */
    public static MongoDatabaseFactory getMongoFactory(String uri, String dbName) {
        MongoClient mongoClient = MongoClients.create(uri);
        MongoDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(mongoClient, dbName);
        log.info("Successfully created SIMPLE_MONGO_FACTORY with \n dbName : {}, \n mongoClient :{}", dbName, mongoClient);
        return factory;
    }
}
