package com.concirrus.certificateservice;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class CertificateServiceApplication {

	private static final Logger logger = LoggerFactory.getLogger(CertificateServiceApplication.class);

	public static void main(String[] args) {
		logger.info("***************** Starting Certificate Service *******************");
		SpringApplication.run(CertificateServiceApplication.class, args);
		logger.info("***************** Certificate Service Started *******************");
	}
}
