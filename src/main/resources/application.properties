#concirrus credentials

server.port=8080

server.servlet.context-path=/core/certificate-issuance-platform-service
mongo.certificate.db=${MONGO_CERTIFICATE_DB:dev_coresystem_certificates}
mongo.policy.uri=mongodb://${MONGO_USERNAME:username}:${MONGO_PASSWORD:password}@${MONGO_IPS:************:27017}/${MONGO_DEFAULT_DB:defaultauthdb}?authSource=admin&connectTimeoutMS=30000

# GCP Configuration
cloud.gcp.project-id=${CLOUD_GCP_PROJECT_ID}

cloud.queue.out.document=${DOCUMENT_QUEUE_PUBLISHER:cs-gcp-dev-document}
cloud.queue.out.submission=${SUBMISSION_QUEUE_PUBLISHER:submission-email-alert-gcp-dev}

management.endpoints.web.base-path=/manage
management.endpoints.web.exposure.include=health, info, prometheus, restart
management.endpoint.info.enabled=true
management.endpoint.health.enabled=true
management.endpoint.health.show-details=always
management.endpoint.restart.enabled=true

spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD}

realm.name.cip=${CIP_REALM_NAME:dev-certificate-platform}