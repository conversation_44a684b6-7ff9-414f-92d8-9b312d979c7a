//package com.concirrus.certificateservice.controller;
//
//import com.concirrus.certificateservice.service.CertificateSSEService;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//public class CertificateSSEControllerTest {
//
//    @Mock
//    private CertificateSSEService certificateSSEService;
//
//    @InjectMocks
//    private CertificateSSEController certificateSSEController;
//
//    private final String clientId = "test-client";
//    private final String userId = "test-user";
//    private final String userRoles = "SUBMISSION_ADMIN,USER";
//
//    @Test
//    public void testSubscribe_Success() {
//        // Arrange
//        SseEmitter mockEmitter = new SseEmitter();
//        when(certificateSSEService.createEmitter(clientId, userId, userRoles)).thenReturn(mockEmitter);
//
//        // Act
//        SseEmitter result = certificateSSEController.subscribe(clientId, userId, userRoles);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(mockEmitter, result);
//        verify(certificateSSEService).createEmitter(clientId, userId, userRoles);
//    }
//
//    @Test
//    public void testSubscribe_NullRoles_Success() {
//        // Arrange
//        SseEmitter mockEmitter = new SseEmitter();
//        when(certificateSSEService.createEmitter(clientId, userId, "")).thenReturn(mockEmitter);
//
//        // Act
//        SseEmitter result = certificateSSEController.subscribe(clientId, userId, "");
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(mockEmitter, result);
//        verify(certificateSSEService).createEmitter(clientId, userId, "");
//    }
//
//    @Test
//    public void testSubscribe_ServiceThrowsException_PropagatesException() {
//        // Arrange
//        RuntimeException exception = new RuntimeException("Test exception");
//        when(certificateSSEService.createEmitter(clientId, userId, userRoles)).thenThrow(exception);
//
//        // Act & Assert
//        try {
//            certificateSSEController.subscribe(clientId, userId, userRoles);
//        } catch (RuntimeException e) {
//            assertEquals(exception, e);
//        }
//
//        verify(certificateSSEService).createEmitter(clientId, userId, userRoles);
//    }
//}