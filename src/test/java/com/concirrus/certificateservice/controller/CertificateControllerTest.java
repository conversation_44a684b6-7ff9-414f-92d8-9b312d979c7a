package com.concirrus.certificateservice.controller;

import com.concirrus.certificateservice.constant.enums.CertificateStatus;
import com.concirrus.certificateservice.dto.CertificateFilterRequest;
import com.concirrus.certificateservice.dto.PagedResponse;
import com.concirrus.certificateservice.service.CertificateService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static com.concirrus.certificateservice.constant.RequestConstant.CLIENT_ID;
import static com.concirrus.certificateservice.constant.RequestConstant.USER_ID;
import static com.concirrus.certificateservice.constant.RequestConstant.USER_ROLES;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
public class CertificateControllerTest {

    @Mock
    private CertificateService certificateService;

    @InjectMocks
    private CertificateController certificateController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private final String clientId = "test-client";
    private final String userId = "test-user";
    private final String userRoles = "SUBMISSION_ADMIN,USER";
    private final String certificateId = "cert-123";

    private Document certificateDocument;
    private List<Document> certificateDocuments;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(certificateController).build();
        objectMapper = new ObjectMapper();

        // Setup test document
        certificateDocument = new Document();
        certificateDocument.put("_id", certificateId);
        certificateDocument.put("clientId", clientId);
        certificateDocument.put("status", "DRAFT");
        certificateDocument.put("data", new Document("field1", "value1"));

        // Setup certificate documents list
        certificateDocuments = new ArrayList<>();
        certificateDocuments.add(certificateDocument);
    }

    @Test
    public void testCreateCertificate_Success() throws Exception {
        // Arrange
        when(certificateService.createCertificate(any(Document.class), eq(clientId), eq(userId)))
            .thenReturn(certificateDocument);

        // Act & Assert
        mockMvc.perform(post("/certificate")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .content("{\"_id\":\"cert-123\",\"status\":\"DRAFT\",\"data\":{\"field1\":\"value1\"}}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId));

        // Verify service was called
        verify(certificateService).createCertificate(any(Document.class), eq(clientId), eq(userId));
    }

    @Test
    public void testUpdateCertificate_Success() throws Exception {
        // Arrange
        when(certificateService.updateCertificate(eq(certificateId), any(Document.class), eq(clientId), eq(userId)))
            .thenReturn(certificateDocument);

        // Act & Assert
        mockMvc.perform(put("/certificate/{certificateId}", certificateId)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .content("{\"_id\":\"cert-123\",\"status\":\"DRAFT\",\"data\":{\"field1\":\"value1\"}}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId));

        // Verify service was called
        verify(certificateService).updateCertificate(eq(certificateId), any(Document.class), eq(clientId), eq(userId));
    }

    @Test
    public void testCreateOrUpdateCertificate_Success() throws Exception {
        // Arrange
        when(certificateService.createOrUpdateCertificate(any(Document.class), eq(clientId), eq(userId)))
            .thenReturn(certificateDocument);

        // Act & Assert
        mockMvc.perform(patch("/certificate")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .content("{\"_id\":\"cert-123\",\"status\":\"DRAFT\",\"data\":{\"field1\":\"value1\"}}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId));

        // Verify service was called
        verify(certificateService).createOrUpdateCertificate(any(Document.class), eq(clientId), eq(userId));
    }

    @Test
    public void testGetCertificateById_Success() throws Exception {
        // Arrange
        when(certificateService.getCertificateById(certificateId, clientId, null))
            .thenReturn(certificateDocument);

        // Act & Assert
        mockMvc.perform(get("/certificate/{certificateId}", certificateId)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .header(USER_ROLES, userRoles))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId));

        // Verify service was called
        verify(certificateService).getCertificateById(certificateId, clientId, null);
    }

    @Test
    public void testGetCertificateById_NonAdminUser_Success() throws Exception {
        // Arrange
        String nonAdminRoles = "USER";
        when(certificateService.getCertificateById(certificateId, clientId, userId))
            .thenReturn(certificateDocument);

        // Act & Assert
        mockMvc.perform(get("/certificate/{certificateId}", certificateId)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .header(USER_ROLES, nonAdminRoles))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId));

        // Verify service was called with userId for non-admin
        verify(certificateService).getCertificateById(certificateId, clientId, userId);
    }

    @Test
    public void testGetAllCertificates_Success() throws Exception {
        // Arrange
        CertificateFilterRequest filterRequest = new CertificateFilterRequest();
        PageRequest pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.ASC, "createdAt"));

        when(certificateService.getAllCertificates(any(CertificateFilterRequest.class),
                                                 any(PageRequest.class),
                                                 eq(clientId),
                                                 isNull()))
            .thenReturn(Pair.of(1L, certificateDocuments));

        // Act & Assert
        mockMvc.perform(post("/certificate/search")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .header(USER_ROLES, userRoles)
                .content("{}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId))
                .andExpect(jsonPath("$.totalItems").value(1));

        // Verify service was called
        verify(certificateService).getAllCertificates(any(CertificateFilterRequest.class),
                                                    any(PageRequest.class),
                                                    eq(clientId),
                                                    isNull());
    }

    @Test
    public void testUpdateCertificateStatus_Success() throws Exception {
        // Arrange
        Document statusUpdateRequest = new Document();
        statusUpdateRequest.put("approvalStatus", "APPROVED");

        when(certificateService.updateCertificateStatus(
                eq(certificateId),
                any(Document.class),
                eq(CertificateStatus.APPROVED),
                isNull(),
                eq(clientId),
                eq(userId)))
            .thenReturn(certificateDocument);

        // Act & Assert
        mockMvc.perform(patch("/certificate/{certificateId}/status", certificateId)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, clientId)
                .header(USER_ID, userId)
                .content("{\"approvalStatus\":\"APPROVED\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0]._id").value(certificateId));

        // Verify service was called
        verify(certificateService).updateCertificateStatus(
                eq(certificateId),
                any(Document.class),
                eq(CertificateStatus.APPROVED),
                isNull(),
                eq(clientId),
                eq(userId));
    }

    @Test
    public void testMissingClientIdHeader_ReturnsBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/certificate/{certificateId}", certificateId)
                .header(USER_ID, userId))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verifyNoInteractions(certificateService);
    }

    @Test
    public void testMissingUserIdHeader_ReturnsBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/certificate")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, clientId)
                .content("{\"_id\":\"cert-123\",\"status\":\"DRAFT\",\"data\":{\"field1\":\"value1\"}}"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verifyNoInteractions(certificateService);
    }
}