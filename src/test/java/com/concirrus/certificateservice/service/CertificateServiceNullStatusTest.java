package com.concirrus.certificateservice.service;

import com.concirrus.certificateservice.dal.CertificateDAL;
import com.concirrus.certificateservice.redis.pubsub.CertificateUpdateRedisPublisher;
import com.concirrus.certificateservice.sal.HullUserSAL;
import com.concirrus.certificateservice.service.impl.CertificateServiceImpl;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.concirrus.certificateservice.constant.DbConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Test class to verify that the NullPointerException fix for status field works correctly.
 */
@ExtendWith(MockitoExtension.class)
public class CertificateServiceNullStatusTest {

    @Mock
    private CertificateDAL certificateDAL;

    @Mock
    private IEventMessageService eventMessageService;

    @Mock
    private HullUserSAL hullUserSAL;

    @Mock
    private CertificateUpdateRedisPublisher redisPublisher;

    @InjectMocks
    private CertificateServiceImpl certificateService;

    private String clientId;
    private String userId;
    private Document certificateRequest;

    @BeforeEach
    void setUp() {
        clientId = "test-client-id";
        userId = "test-user-id";
        certificateRequest = new Document();
        
        // Initialize the service with mock dependencies
        certificateService = new CertificateServiceImpl(
            certificateDAL,
            "test-document-queue",
            eventMessageService,
            "test-submission-queue",
            hullUserSAL,
            redisPublisher
        );
    }

    @Test
    void testCreateCertificate_WithNullStatus_ShouldNotThrowNullPointerException() {
        // Arrange
        certificateRequest.put(POLICY_NUMBER, "TEST-POLICY-001");
        certificateRequest.put(INSURED_CLIENT_ID, "INSURED123");
        // Intentionally NOT setting STATUS field to simulate the null pointer scenario
        
        when(certificateDAL.save(any(Document.class))).thenReturn(certificateRequest);
        when(certificateDAL.getPreviousCertificatesCountByPolicyNumber(any(), any())).thenReturn(0L);

        // Act & Assert
        assertDoesNotThrow(() -> {
            Document result = certificateService.createCertificate(certificateRequest, clientId, userId);
            assertNotNull(result);
        });
    }

    @Test
    void testCreateCertificate_WithEmptyStatus_ShouldNotThrowNullPointerException() {
        // Arrange
        certificateRequest.put(POLICY_NUMBER, "TEST-POLICY-002");
        certificateRequest.put(INSURED_CLIENT_ID, "INSURED456");
        certificateRequest.put(STATUS, (String) null); // Explicitly set to null
        
        when(certificateDAL.save(any(Document.class))).thenReturn(certificateRequest);
        when(certificateDAL.getPreviousCertificatesCountByPolicyNumber(any(), any())).thenReturn(0L);

        // Act & Assert
        assertDoesNotThrow(() -> {
            Document result = certificateService.createCertificate(certificateRequest, clientId, userId);
            assertNotNull(result);
        });
    }

    @Test
    void testUpdateCertificate_WithNullStatus_ShouldNotThrowNullPointerException() {
        // Arrange
        String certificateId = "test-certificate-id";
        certificateRequest.put(POLICY_NUMBER, "TEST-POLICY-003");
        certificateRequest.put(INSURED_CLIENT_ID, "INSURED789");
        // Intentionally NOT setting STATUS field to simulate the null pointer scenario
        
        when(certificateDAL.updateCertificateById(any(), any(), any(), any())).thenReturn(certificateRequest);
        when(certificateDAL.getPreviousCertificatesCountByPolicyNumber(any(), any())).thenReturn(0L);

        // Act & Assert
        assertDoesNotThrow(() -> {
            Document result = certificateService.updateCertificate(certificateId, certificateRequest, clientId, userId);
            assertNotNull(result);
        });
    }
}
