//package com.concirrus.certificateservice.service;
//
//import com.concirrus.certificateservice.service.impl.CertificateSSEServiceImpl;
//import com.concirrus.certificateservice.utils.CertificateControllerUtils;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.ArgumentCaptor;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.data.redis.connection.Message;
//import org.springframework.data.redis.connection.MessageListener;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.listener.ChannelTopic;
//import org.springframework.data.redis.listener.RedisMessageListenerContainer;
//import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//
//import java.io.IOException;
//import java.lang.reflect.Field;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.ConcurrentHashMap;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//public class CertificateSSEServiceImplTests {
//
//    @Mock
//    private RedisTemplate<String, String> redisTemplate;
//
//    @Mock
//    private RedisConnectionFactory connectionFactory;
//
//    @Mock
//    private ObjectMapper objectMapper;
//
//    @Mock
//    private SseEmitter sseEmitter;
//
//    @InjectMocks
//    private CertificateSSEServiceImpl certificateSSEService;
//
//    private final String clientId = "test-client";
//    private final String userId = "test-user";
//    private final String adminRoles = "SUBMISSION_ADMIN,USER";
//    private final String nonAdminRoles = "USER";
//    private final String certificateId = "cert-123";
//
//    @BeforeEach
//    public void setup() throws Exception {
//        // Mock the container creation in init() method
//        RedisMessageListenerContainer container = mock(RedisMessageListenerContainer.class);
//
//        // Set the mocked container to the service
//        Field containerField = CertificateSSEServiceImpl.class.getDeclaredField("container");
//        containerField.setAccessible(true);
//        containerField.set(certificateSSEService, container);
//    }
//
////    @Test
////    public void testCreateEmitter_AdminUser_Success() throws IOException {
////        // Arrange
////        when(sseEmitter.send(any(SseEmitter.SseEventBuilder.class))).thenReturn(sseEmitter);
////
////        // Act
////        SseEmitter result = certificateSSEService.createEmitter(clientId, userId, adminRoles);
////
////        // Assert
////        assertNotNull(result);
////        verify(sseEmitter).send(any(SseEmitter.SseEventBuilder.class));
////
////        // Verify emitter is stored
////        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
////        assertTrue(emitters.containsKey(clientId));
////        assertTrue(emitters.get(clientId).containsKey(userId));
////    }
//
////    @Test
////    public void testCreateEmitter_NonAdminUser_ReturnsEmitterButDoesNotStore() throws IOException {
////        // Act
////        SseEmitter result = certificateSSEService.createEmitter(clientId, userId, nonAdminRoles);
////
////        // Assert
////        assertNotNull(result);
////
////        // Verify emitter is not stored
////        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
////        assertTrue(emitters.isEmpty());
////    }
//
//    @Test
////    public void testCreateEmitter_SendError_RemovesEmitter() throws IOException {
////        // Arrange
////        doThrow(new IOException("Test exception")).when(sseEmitter).send(any(SseEmitter.SseEventBuilder.class));
////
////        // Act
////        SseEmitter result = certificateSSEService.createEmitter(clientId, userId, adminRoles);
////
////        // Assert
////        assertNotNull(result);
////
////        // Verify emitter is not stored due to error
////        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
////        assertTrue(emitters.isEmpty() || !emitters.containsKey(clientId) ||
////                  !emitters.get(clientId).containsKey(userId));
////    }
//
//    @Test
//    public void testSendCertificateStatusUpdateEvent_Success() throws JsonProcessingException {
//        // Arrange
//        Map<String, String> expectedMessage = new HashMap<>();
//        expectedMessage.put("clientId", clientId);
//        expectedMessage.put("certificateId", certificateId);
//
//        String jsonMessage = "{\"clientId\":\"test-client\",\"certificateId\":\"cert-123\"}";
//        when(objectMapper.writeValueAsString(any(Map.class))).thenReturn(jsonMessage);
//
//        // Act
//        certificateSSEService.sendCertificateStatusUpdateEvent(clientId, certificateId);
//
//        // Assert
//        verify(objectMapper).writeValueAsString(argThat(map ->
//            map instanceof Map &&
//            ((Map) map).get("clientId").equals(clientId) &&
//            ((Map) map).get("certificateId").equals(certificateId)
//        ));
//        verify(redisTemplate).convertAndSend(eq("certificate-status-updates"), eq(jsonMessage));
//    }
//
//    @Test
//    public void testSendCertificateStatusUpdateEvent_JsonProcessingException() throws JsonProcessingException {
//        // Arrange
//        when(objectMapper.writeValueAsString(any(Map.class))).thenThrow(new JsonProcessingException("Test exception") {});
//
//        // Act & Assert - should not throw exception
//        assertDoesNotThrow(() -> certificateSSEService.sendCertificateStatusUpdateEvent(clientId, certificateId));
//
//        // Verify Redis publish was not called
//        verify(redisTemplate, never()).convertAndSend(anyString(), anyString());
//    }
//
//    @Test
//    public void testProcessStatusUpdateEvent_NoEmitters() throws Exception {
//        // Arrange - ensure no emitters
//        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
//        emitters.clear();
//
//        // Create message and invoke listener directly
//        String messageBody = "{\"clientId\":\"test-client\",\"certificateId\":\"cert-123\"}";
//        when(objectMapper.readValue(eq(messageBody), eq(Map.class))).thenReturn(
//            Map.of("clientId", clientId, "certificateId", certificateId)
//        );
//
//        // Get the message listener
//        MessageListener listener = getMessageListener();
//        Message message = mock(Message.class);
//        when(message.getBody()).thenReturn(messageBody.getBytes());
//
//        // Act
//        listener.onMessage(message, null);
//
//        // Assert - no exceptions, nothing to verify since there are no emitters
//    }
//
//    @Test
//    public void testProcessStatusUpdateEvent_WithEmitters() throws Exception {
//        // Arrange - add an emitter
//        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
//        Map<String, SseEmitter> userEmitters = new ConcurrentHashMap<>();
//        userEmitters.put(userId, sseEmitter);
//        emitters.put(clientId, userEmitters);
//
//        // Create message and invoke listener directly
//        String messageBody = "{\"clientId\":\"test-client\",\"certificateId\":\"cert-123\"}";
//        when(objectMapper.readValue(eq(messageBody), eq(Map.class))).thenReturn(
//            Map.of("clientId", clientId, "certificateId", certificateId)
//        );
//
//        // Get the message listener
//        MessageListener listener = getMessageListener();
//        Message message = mock(Message.class);
//        when(message.getBody()).thenReturn(messageBody.getBytes());
//
//        // Act
//        listener.onMessage(message, null);
//
//        // Assert
//        verify(sseEmitter).send(any(SseEmitter.SseEventBuilder.class));
//    }
//
//    @Test
//    public void testProcessStatusUpdateEvent_EmitterThrowsException() throws Exception {
//        // Arrange - add an emitter
//        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
//        Map<String, SseEmitter> userEmitters = new ConcurrentHashMap<>();
//        userEmitters.put(userId, sseEmitter);
//        emitters.put(clientId, userEmitters);
//
//        // Make emitter throw exception
//        doThrow(new IOException("Test exception")).when(sseEmitter).send(any(SseEmitter.SseEventBuilder.class));
//
//        // Create message and invoke listener directly
//        String messageBody = "{\"clientId\":\"test-client\",\"certificateId\":\"cert-123\"}";
//        when(objectMapper.readValue(eq(messageBody), eq(Map.class))).thenReturn(
//            Map.of("clientId", clientId, "certificateId", certificateId)
//        );
//
//        // Get the message listener
//        MessageListener listener = getMessageListener();
//        Message message = mock(Message.class);
//        when(message.getBody()).thenReturn(messageBody.getBytes());
//
//        // Act
//        listener.onMessage(message, null);
//
//        // Assert - emitter should be removed
//        assertTrue(emitters.isEmpty() || !emitters.get(clientId).containsKey(userId));
//    }
//
////    @Test
////    public void testRemoveEmitter_Success() throws Exception {
////        // Arrange - add an emitter
////        Map<String, Map<String, SseEmitter>> emitters = getEmittersMap();
////        Map<String, SseEmitter> userEmitters = new ConcurrentHashMap<>();
////        userEmitters.put(userId, sseEmitter);
////        emitters.put(clientId, userEmitters);
////
////        // Act - call removeEmitter via reflection
////        Field field = CertificateSSEServiceImpl.class.getDeclaredField("removeEmitter");
////        field.setAccessible(true);
////        field.invoke(certificateSSEService, clientId, userId);
////
////        // Assert
////        assertTrue(emitters.isEmpty() || !emitters.get(clientId).containsKey(userId));
////    }
//
//    @Test
//    public void testMessageListenerException() throws Exception {
//        // Arrange
//        String messageBody = "{\"clientId\":\"test-client\",\"certificateId\":\"cert-123\"}";
//        when(objectMapper.readValue(eq(messageBody), eq(Map.class)))
//            .thenThrow(new RuntimeException("Test exception"));
//
//        // Get the message listener
//        MessageListener listener = getMessageListener();
//        Message message = mock(Message.class);
//        when(message.getBody()).thenReturn(messageBody.getBytes());
//
//        // Act & Assert - should not throw exception
//        assertDoesNotThrow(() -> listener.onMessage(message, null));
//    }
//
//    // Helper methods to access private fields
//    @SuppressWarnings("unchecked")
//    private Map<String, Map<String, SseEmitter>> getEmittersMap() throws Exception {
//        Field emittersField = CertificateSSEServiceImpl.class.getDeclaredField("emitters");
//        emittersField.setAccessible(true);
//        return (Map<String, Map<String, SseEmitter>>) emittersField.get(certificateSSEService);
//    }
//
//    private MessageListener getMessageListener() throws Exception {
//        // Create a container to capture the listener
//        RedisMessageListenerContainer container = mock(RedisMessageListenerContainer.class);
//
//        // Capture the listener when addMessageListener is called
//        ArgumentCaptor<MessageListener> listenerCaptor = ArgumentCaptor.forClass(MessageListener.class);
//
//        // Reset and recreate the service to capture the listener
//        certificateSSEService = new CertificateSSEServiceImpl();
//
//        // Set the mocked dependencies
//        Field redisTemplateField = CertificateSSEServiceImpl.class.getDeclaredField("redisTemplate");
//        redisTemplateField.setAccessible(true);
//        redisTemplateField.set(certificateSSEService, redisTemplate);
//
//        Field connectionFactoryField = CertificateSSEServiceImpl.class.getDeclaredField("connectionFactory");
//        connectionFactoryField.setAccessible(true);
//        connectionFactoryField.set(certificateSSEService, connectionFactory);
//
//        Field objectMapperField = CertificateSSEServiceImpl.class.getDeclaredField("objectMapper");
//        objectMapperField.setAccessible(true);
//        objectMapperField.set(certificateSSEService, objectMapper);
//
//        // Set the container
//        Field containerField = CertificateSSEServiceImpl.class.getDeclaredField("container");
//        containerField.setAccessible(true);
//        containerField.set(certificateSSEService, container);
//
//        // Mock the init method to capture the listener
//        doNothing().when(container).setConnectionFactory(any());
//        doNothing().when(container).addMessageListener(listenerCaptor.capture(), any(ChannelTopic.class));
//        doNothing().when(container).start();
//
//        // Call init to register the listener
//        certificateSSEService.init();
//
//        // Return the captured listener
//        return listenerCaptor.getValue();
//    }
//}