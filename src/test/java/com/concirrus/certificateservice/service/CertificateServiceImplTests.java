//package com.concirrus.certificateservice.service;
//
//import com.concirrus.certificateservice.constant.enums.CertificateStatus;
//import com.concirrus.certificateservice.dal.CertificateDAL;
//import com.concirrus.certificateservice.dto.CertificateFilterRequest;
//import com.concirrus.certificateservice.dto.UserEntity;
//import com.concirrus.certificateservice.model.exception.BadRequestException;
//import com.concirrus.certificateservice.model.exception.NoDataFoundException;
//import com.concirrus.certificateservice.sal.HullUserSAL;
//import com.concirrus.certificateservice.service.impl.CertificateServiceImpl;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import org.apache.commons.lang3.tuple.Pair;
//import org.bson.Document;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.ArgumentCaptor;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.data.domain.PageRequest;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.UUID;
//
//import static com.concirrus.certificateservice.constant.DbConstants.*;
//import static com.concirrus.certificateservice.constant.enums.CertificateStatus.*;
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//public class CertificateServiceImplTests {
//
//    @Mock
//    private CertificateDAL certificateDAL;
//
//    @Mock
//    private IEventMessageService eventMessageService;
//
//    @Mock
//    private HullUserSAL hullUserSAL;
//
//    @Mock
//    private CertificateSSEService certificateSSEService;
//
//    @InjectMocks
//    private CertificateServiceImpl certificateService;
//
//    private final String clientId = "test-client";
//    private final String userId = "test-user";
//    private final String certificateId = "cert-123";
//    private final String documentId = "doc-123";
//    private final String policyNumber = "POL123";
//    private final String insuredClientId = "insured-123";
//
//    private Document certificateRequest;
//    private Document certificateDocument;
//    private List<Document> certificateDocuments;
//    private UserEntity userEntity;
//
//    @BeforeEach
//    public void setup() {
//        // Setup test document
//        certificateRequest = new Document();
//        certificateRequest.put(CERTIFICATE_ID, certificateId);
//        certificateRequest.put(DOCUMENT_ID, documentId);
//        certificateRequest.put(POLICY_NUMBER, policyNumber);
//        certificateRequest.put(INSURED_CLIENT_ID, insuredClientId);
//        certificateRequest.put(STATUS, DRAFT.getValue());
//        certificateRequest.put(INVOICE_VALUE, 5000.0);
//        certificateRequest.put(MINIMUM_PER_CERTIFICATE, 1000.0);
//        certificateRequest.put(COMMODITY_LIMIT, 10000.0);
//        certificateRequest.put(CONVEYANCE_LIMIT, 20000.0);
//        certificateRequest.put(QUOTE_STRUCTURE, "DEPOSIT_PREMIUM_ADJUSTED_ANNUALLY");
//
//        // Setup saved certificate document
//        certificateDocument = new Document(certificateRequest);
//        certificateDocument.put(CREATED_AT, LocalDateTime.now());
//        certificateDocument.put(CREATED_BY, userId);
//
//        // Setup certificate documents list
//        certificateDocuments = new ArrayList<>();
//        certificateDocuments.add(certificateDocument);
//
//        // Setup user entity
//        userEntity = new UserEntity();
//        userEntity.setFullName("Test User");
//        userEntity.setEmail("<EMAIL>");
//    }
//
//    @Test
//    public void testCreateCertificate_Success() {
//        // Arrange
//        when(certificateDAL.save(any(Document.class))).thenReturn(certificateDocument);
//        when(hullUserSAL.getUserByKeycloakId(userId, true, clientId)).thenReturn(userEntity);
//        when(certificateDAL.getPreviousCertificatesCountByPolicyNumber(policyNumber, clientId)).thenReturn(0L);
//
//        // Act
//        Document result = certificateService.createCertificate(certificateRequest, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(certificateId, result.getString(CERTIFICATE_ID));
//
//        // Verify DAL was called with correct data
//        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
//        verify(certificateDAL).save(documentCaptor.capture());
//
//        Document savedDocument = documentCaptor.getValue();
//        assertEquals(clientId, savedDocument.getString(MONGO_CLIENT_ID));
//        assertEquals(userId, savedDocument.getString(CREATED_BY));
//        assertNotNull(savedDocument.get(CREATED_AT));
//        assertEquals("Test User", savedDocument.getString(NAME));
//        assertEquals("<EMAIL>", savedDocument.getString(EMAIL));
//    }
//
//    @Test
//    public void testUpdateCertificate_ApprovalNotRequired_Success() {
//        // Arrange
//        certificateRequest.put(APPROVAL_REQUIRED, false);
//
//        when(certificateDAL.updateCertificateById(eq(certificateId), any(Document.class), eq(clientId), eq(userId)))
//            .thenReturn(certificateDocument);
//
//        // Act
//        Document result = certificateService.updateCertificate(certificateId, certificateRequest, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(certificateId, result.getString(CERTIFICATE_ID));
//
//        // Verify document was updated with correct status
//        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
//        verify(certificateDAL).updateCertificateById(eq(certificateId), documentCaptor.capture(), eq(clientId), eq(userId));
//
//        Document updatedDocument = documentCaptor.getValue();
//        assertEquals(ISSUED.getValue(), updatedDocument.getString(STATUS));
//        assertNotNull(updatedDocument.getString(GENERATED_AT));
//        assertEquals(userId, updatedDocument.getString(UPDATED_BY));
//        assertNotNull(updatedDocument.get(UPDATED_AT));
//
//        // Verify event message was published
//        verify(eventMessageService).publishMessage(anyString(), anyString());
//    }
//
//    @Test
//    public void testUpdateCertificate_ApprovalRequired_SetsPendingStatus() {
//        // Arrange
//        certificateRequest.put(APPROVAL_REQUIRED, true);
//
//        when(certificateDAL.updateCertificateById(eq(certificateId), any(Document.class), eq(clientId), eq(userId)))
//            .thenReturn(certificateDocument);
//
//        // Act
//        Document result = certificateService.updateCertificate(certificateId, certificateRequest, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//
//        // Verify document was updated with correct status
//        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
//        verify(certificateDAL).updateCertificateById(eq(certificateId), documentCaptor.capture(), eq(clientId), eq(userId));
//
//        Document updatedDocument = documentCaptor.getValue();
//        assertEquals(PENDING.getValue(), updatedDocument.getString(STATUS));
//
//        // Verify no event message was published
//        verify(eventMessageService, never()).publishMessage(anyString(), anyString());
//    }
//
//    @Test
//    public void testUpdateCertificate_ValidationFails_ThrowsException() {
//        // Arrange
//        certificateRequest.put(INVOICE_VALUE, 500.0); // Below minimum
//
//        // Act & Assert
//        Exception exception = assertThrows(BadRequestException.class, () ->
//            certificateService.updateCertificate(certificateId, certificateRequest, clientId, userId)
//        );
//
//        assertTrue(exception.getMessage().contains("lower than"));
//
//        // Verify DAL was not called
//        verify(certificateDAL, never()).updateCertificateById(anyString(), any(Document.class), anyString(), anyString());
//    }
//
//    @Test
//    public void testCreateOrUpdateCertificate_WithExistingId_UpdatesExisting() {
//        // Arrange
//        when(certificateDAL.updateCertificateById(eq(certificateId), any(Document.class), eq(clientId), eq(userId)))
//            .thenReturn(certificateDocument);
//
//        // Act
//        Document result = certificateService.createOrUpdateCertificate(certificateRequest, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(certificateId, result.getString(CERTIFICATE_ID));
//
//        // Verify update was called
//        verify(certificateDAL).updateCertificateById(eq(certificateId), any(Document.class), eq(clientId), eq(userId));
//
//        // Verify save was not called
//        verify(certificateDAL, never()).save(any(Document.class));
//    }
//
//    @Test
//    public void testCreateOrUpdateCertificate_WithoutId_CreatesNew() {
//        // Arrange
//        certificateRequest.remove(CERTIFICATE_ID);
//
//        when(certificateDAL.save(any(Document.class))).thenReturn(certificateDocument);
//        when(hullUserSAL.getUserByKeycloakId(userId, true, clientId)).thenReturn(userEntity);
//        when(certificateDAL.getPreviousCertificatesCountByPolicyNumber(policyNumber, clientId)).thenReturn(0L);
//
//        // Act
//        Document result = certificateService.createOrUpdateCertificate(certificateRequest, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//
//        // Verify save was called
//        verify(certificateDAL).save(any(Document.class));
//
//        // Verify update was not called
//        verify(certificateDAL, never()).updateCertificateById(anyString(), any(Document.class), anyString(), anyString());
//    }
//
//    @Test
//    public void testGetCertificateById_Success() {
//        // Arrange
//        when(certificateDAL.getCertificateById(certificateId, clientId, userId)).thenReturn(certificateDocument);
//
//        // Act
//        Document result = certificateService.getCertificateById(certificateId, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(certificateId, result.getString(CERTIFICATE_ID));
//
//        // Verify DAL was called
//        verify(certificateDAL).getCertificateById(certificateId, clientId, userId);
//    }
//
//    @Test
//    public void testGetCertificateById_NotFound_ThrowsException() {
//        // Arrange
//        when(certificateDAL.getCertificateById(certificateId, clientId, userId)).thenReturn(null);
//
//        // Act & Assert
//        Exception exception = assertThrows(NoDataFoundException.class, () ->
//            certificateService.getCertificateById(certificateId, clientId, userId)
//        );
//
//        assertTrue(exception.getMessage().contains("Certificate not found"));
//    }
//
//    @Test
//    public void testGetAllCertificates_Success() {
//        // Arrange
//        CertificateFilterRequest filterRequest = new CertificateFilterRequest();
//        PageRequest pageRequest = PageRequest.of(0, 10);
//
//        when(certificateDAL.getAllCertificatesBy(filterRequest, pageRequest, clientId, userId))
//            .thenReturn(Pair.of(1L, certificateDocuments));
//
//        // Act
//        Pair<Long, List<Document>> result = certificateService.getAllCertificates(filterRequest, pageRequest, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(1L, result.getLeft());
//        assertEquals(1, result.getRight().size());
//        assertEquals(certificateId, result.getRight().get(0).getString(CERTIFICATE_ID));
//
//        // Verify DAL was called
//        verify(certificateDAL).getAllCertificatesBy(filterRequest, pageRequest, clientId, userId);
//    }
//
//    @Test
//    public void testSendCertificateEmail_Success() throws JsonProcessingException {
//        // Arrange
//        String name = "Test User";
//        String email = "<EMAIL>";
//        String template = "CERTIFICATE_APPROVED";
//        String lob = "LOGISTICS";
//
//        // Act
//        certificateService.sendCertificateEmail(name, email, template, clientId, lob);
//
//        // Assert
//        // Verify event message was published
//        verify(eventMessageService).publishMessage(anyString(), anyString());
//    }
//
//    @Test
//    public void testGetCertificatesRequestsCount_Success() {
//        // Arrange
//        Boolean approvalRequired = true;
//        List<CertificateStatus> approvalStatus = Arrays.asList(PENDING);
//        Boolean changesRequired = false;
//        List<CertificateStatus> changeRequestStatus = null;
//        List<CertificateStatus> status = Arrays.asList(PENDING);
//
//        when(certificateDAL.getCertificatesRequestsCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, userId, status))
//            .thenReturn(5L);
//
//        // Act
//        long result = certificateService.getCertificatesRequestsCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, userId, status);
//
//        // Assert
//        assertEquals(5L, result);
//
//        // Verify DAL was called
//        verify(certificateDAL).getCertificatesRequestsCount(clientId, approvalRequired, approvalStatus, changesRequired, changeRequestStatus, userId, status);
//    }
//
//    @Test
//    public void testUpdateCertificateStatus_ApproveStatus_Success() {
//        // Arrange
//        when(certificateDAL.getCertificateById(certificateId, clientId, null)).thenReturn(certificateDocument);
//        when(certificateDAL.updateCertificateById(eq(certificateId), any(Document.class), eq(clientId), isNull()))
//            .thenReturn(certificateDocument);
//
//        // Act
//        Document result = certificateService.updateCertificateStatus(certificateId, null, APPROVED, null, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//
//        // Verify document was updated with correct status
//        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
//        verify(certificateDAL).updateCertificateById(eq(certificateId), documentCaptor.capture(), eq(clientId), isNull());
//
//        Document updatedDocument = documentCaptor.getValue();
//        assertEquals(ISSUED.getValue(), updatedDocument.getString(STATUS));
//        assertEquals(APPROVED.getValue(), updatedDocument.getString(APPROVAL_STATUS));
//        assertNotNull(updatedDocument.getString(GENERATED_AT));
//
//        // Verify event message was published
//        verify(eventMessageService).publishMessage(anyString(), anyString());
//
//        // Verify SSE notification was sent
//        verify(certificateSSEService).sendCertificateStatusUpdateEvent(clientId, certificateId);
//    }
//
//    @Test
//    public void testUpdateCertificateStatus_ApproveChangeRequest_Success() {
//        // Arrange
//        when(certificateDAL.getCertificateById(certificateId, clientId, null)).thenReturn(certificateDocument);
//        when(certificateDAL.updateCertificateById(eq(certificateId), any(Document.class), eq(clientId), isNull()))
//            .thenReturn(certificateDocument);
//
//        // Act
//        Document result = certificateService.updateCertificateStatus(certificateId, null, null, APPROVED, clientId, userId);
//
//        // Assert
//        assertNotNull(result);
//
//        // Verify document was updated with correct status
//        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
//        verify(certificateDAL).updateCertificateById(eq(certificateId), documentCaptor.capture(), eq(clientId), isNull());
//
//        Document updatedDocument = documentCaptor.getValue();
//        assertEquals(ISSUED.getValue(), updatedDocument.getString(STATUS));
//        assertEquals(APPROVED.getValue(), updatedDocument.getString(CHANGE_REQUEST_STATUS));
//        assertNotNull(updatedDocument.getString(GENERATED_AT));
//        assertEquals(false, updatedDocument.getBoolean(CHANGES_REQUIRED));
//
//        // Verify event message was published
//        verify(eventMessageService).publishMessage(anyString(), anyString());
//
//        // Verify SSE notification was sent
//        verify(certificateSSEService).sendCertificateStatusUpdateEvent(clientId, certificateId);
//    }
//
//    @Test
//    public void testUpdateCertificateStatus_CertificateNotFound_ThrowsException() {
//        // Arrange
//        when(certificateDAL.getCertificateById(certificateId, clientId, null)).thenReturn(null);
//
//        // Act & Assert
//        Exception exception = assertThrows(NoDataFoundException.class, () ->
//            certificateService.updateCertificateStatus(certificateId, null, APPROVED, null, clientId, userId)
//        );
//
//        assertTrue(exception.getMessage().contains("Certificate not found"));
//
//        // Verify DAL update was not called
//        verify(certificateDAL, never()).updateCertificateById(anyString(), any(Document.class), anyString(), any());
//    }
//}