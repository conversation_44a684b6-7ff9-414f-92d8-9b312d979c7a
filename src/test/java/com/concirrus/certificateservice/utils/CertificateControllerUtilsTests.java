package com.concirrus.certificateservice.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Stream;

import static com.concirrus.certificateservice.constant.RequestConstant.SUBMISSION_ADMIN;
import static com.concirrus.certificateservice.constant.RequestConstant.UNDERWRITER;
import static org.junit.jupiter.api.Assertions.*;

public class CertificateControllerUtilsTests {

    @Test
    public void testParseUserRoles_WithValidRoles() {
        // Arrange
        String rolesString = "ROLE1,ROLE2,ROLE3";

        // Act
        Set<String> result = CertificateControllerUtils.parseUserRoles(rolesString);

        // Assert
        assertEquals(3, result.size());
        assertTrue(result.contains("ROLE1"));
        assertTrue(result.contains("ROLE2"));
        assertTrue(result.contains("ROLE3"));
    }

    @Test
    public void testParseUserRoles_WithEmptyString() {
        // Act
        Set<String> result = CertificateControllerUtils.parseUserRoles("");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseUserRoles_WithNull() {
        // Act
        Set<String> result = CertificateControllerUtils.parseUserRoles(null);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseUserRoles_WithWhitespace() {
        // Arrange
        String rolesString = "ROLE1, ROLE2 , ROLE3";

        // Act
        Set<String> result = CertificateControllerUtils.parseUserRoles(rolesString);

        // Assert
        assertEquals(3, result.size());
        assertTrue(result.contains("ROLE1"));
        assertTrue(result.contains("ROLE2"));
        assertTrue(result.contains("ROLE3"));
    }

    @ParameterizedTest
    @MethodSource("provideRoleSetsForAdminCheck")
    public void testHasAdminRole(Set<String> roles, boolean expected) {
        // Act
        boolean result = CertificateControllerUtils.hasAdminRole(roles);

        // Assert
        assertEquals(expected, result);
    }

    private static Stream<Arguments> provideRoleSetsForAdminCheck() {
        return Stream.of(
            Arguments.of(Set.of(SUBMISSION_ADMIN), true),
            Arguments.of(Set.of(UNDERWRITER), true),
            Arguments.of(Set.of(SUBMISSION_ADMIN, "OTHER_ROLE"), true),
            Arguments.of(Set.of(UNDERWRITER, "OTHER_ROLE"), true),
            Arguments.of(Set.of("OTHER_ROLE"), false),
            Arguments.of(new HashSet<String>(), false),
            Arguments.of(null, false)
        );
    }
}