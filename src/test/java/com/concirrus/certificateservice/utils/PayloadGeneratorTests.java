package com.concirrus.certificateservice.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.Document;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.concirrus.certificateservice.constant.DbConstants.*;
import static org.junit.jupiter.api.Assertions.*;

public class PayloadGeneratorTests {

    @Test
    public void testCurrencyFormatting_WithValidNumbers() throws Exception {
        // Arrange
        Document certificateRequest = new Document();
        certificateRequest.put(CERTIFICATE_ID, "test-cert-123");
        certificateRequest.put(GENERATED_AT, "01-15-2024, 10:30");
        certificateRequest.put(DOCUMENT_ID, "doc-123");
        certificateRequest.put(CERTIFICATE_NUMBER, "CERT-001");
        certificateRequest.put(CERTIFICATE_NAME, "Test Certificate");
        certificateRequest.put(POLICY_NUMBER, "POL-123");
        certificateRequest.put(CERTIFICATE_HOLDER_NAME, "Test Holder");
        certificateRequest.put(INSURABLE_VALUE, 100000); // Should become $100,000.00
        certificateRequest.put(INVOICE_VALUE, 250000.50); // Should become $250,000.50
        certificateRequest.put(COVERAGE, "Test Coverage");
        certificateRequest.put(CONVEYANCE, "Test Conveyance");
        certificateRequest.put(CURRENCY, "USD");
        certificateRequest.put(COMPANY_NAME, "Test Company");

        // Act
        String result = PayloadGenerator.generateCertificatePayload("client-123", certificateRequest);

        // Assert
        assertNotNull(result);

        // Parse the JSON to verify currency formatting
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> payload = mapper.readValue(result, Map.class);
        Map<String, Object> certificatePayload = (Map<String, Object>) payload.get("certificatePayload");

        assertEquals("$100,000.00", certificatePayload.get(INSURABLE_VALUE));
        assertEquals("$250,000.50", certificatePayload.get(INVOICE_VALUE));
    }

    @Test
    public void testCurrencyFormatting_WithZeroValues() throws Exception {
        // Arrange
        Document certificateRequest = new Document();
        certificateRequest.put(CERTIFICATE_ID, "test-cert-123");
        certificateRequest.put(GENERATED_AT, "01-15-2024, 10:30");
        certificateRequest.put(DOCUMENT_ID, "doc-123");
        certificateRequest.put(CERTIFICATE_NUMBER, "CERT-001");
        certificateRequest.put(CERTIFICATE_NAME, "Test Certificate");
        certificateRequest.put(POLICY_NUMBER, "POL-123");
        certificateRequest.put(CERTIFICATE_HOLDER_NAME, "Test Holder");
        certificateRequest.put(INSURABLE_VALUE, 0); // Should become $0
        certificateRequest.put(INVOICE_VALUE, "0"); // Should become $0
        certificateRequest.put(COVERAGE, "Test Coverage");
        certificateRequest.put(CONVEYANCE, "Test Conveyance");
        certificateRequest.put(CURRENCY, "USD");
        certificateRequest.put(COMPANY_NAME, "Test Company");

        // Act
        String result = PayloadGenerator.generateCertificatePayload("client-123", certificateRequest);

        // Assert
        assertNotNull(result);

        // Parse the JSON to verify currency formatting
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> payload = mapper.readValue(result, Map.class);
        Map<String, Object> certificatePayload = (Map<String, Object>) payload.get("certificatePayload");

        assertEquals("$0", certificatePayload.get(INSURABLE_VALUE));
        assertEquals("$0", certificatePayload.get(INVOICE_VALUE));
    }

    @Test
    public void testCurrencyFormatting_WithNullValues() throws Exception {
        // Arrange
        Document certificateRequest = new Document();
        certificateRequest.put(CERTIFICATE_ID, "test-cert-123");
        certificateRequest.put(GENERATED_AT, "01-15-2024, 10:30");
        certificateRequest.put(DOCUMENT_ID, "doc-123");
        certificateRequest.put(CERTIFICATE_NUMBER, "CERT-001");
        certificateRequest.put(CERTIFICATE_NAME, "Test Certificate");
        certificateRequest.put(POLICY_NUMBER, "POL-123");
        certificateRequest.put(CERTIFICATE_HOLDER_NAME, "Test Holder");
        // INSURABLE_VALUE and INVOICE_VALUE are null (not set)
        certificateRequest.put(COVERAGE, "Test Coverage");
        certificateRequest.put(CONVEYANCE, "Test Conveyance");
        certificateRequest.put(CURRENCY, "USD");
        certificateRequest.put(COMPANY_NAME, "Test Company");

        // Act
        String result = PayloadGenerator.generateCertificatePayload("client-123", certificateRequest);

        // Assert
        assertNotNull(result);

        // Parse the JSON to verify currency formatting
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> payload = mapper.readValue(result, Map.class);
        Map<String, Object> certificatePayload = (Map<String, Object>) payload.get("certificatePayload");

        assertEquals("$0", certificatePayload.get(INSURABLE_VALUE));
        assertEquals("$0", certificatePayload.get(INVOICE_VALUE));
    }
}
